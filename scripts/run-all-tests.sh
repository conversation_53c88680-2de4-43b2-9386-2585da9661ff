#!/bin/bash

# Comprehensive Test Runner for Vibe Check Monorepo
# Runs both backend and frontend test suites

set -e  # Exit on any error

echo "🧪 Running Vibe Check Test Suite"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Please run this script from the monorepo root directory"
    exit 1
fi

# Backend Tests
print_status "Running Backend Tests (Python + Pytest)..."
echo "=============================================="

cd backend

# Check if virtual environment exists
if [ ! -d ".venv" ] && [ ! -d "venv" ] && [ -z "$VIRTUAL_ENV" ]; then
    print_warning "No virtual environment detected. Consider activating one."
fi

# Install dependencies if needed
if [ ! -f ".pytest_cache" ]; then
    print_status "Installing backend test dependencies..."
    pip install -q pytest pytest-mock pytest-flask
fi

# Run backend tests
if pytest tests/ -v --tb=short; then
    print_success "Backend tests passed! ✅"
    BACKEND_SUCCESS=true
else
    print_error "Backend tests failed! ❌"
    BACKEND_SUCCESS=false
fi

echo ""

# Frontend Tests
print_status "Running Frontend Tests (Jest + React Testing Library)..."
echo "========================================================="

cd ../frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Run frontend tests
if npm test -- --watchAll=false --verbose; then
    print_success "Frontend tests passed! ✅"
    FRONTEND_SUCCESS=true
else
    print_error "Frontend tests failed! ❌"
    FRONTEND_SUCCESS=false
fi

cd ..

echo ""
echo "🏁 Test Summary"
echo "==============="

if [ "$BACKEND_SUCCESS" = true ]; then
    print_success "Backend: All tests passed"
else
    print_error "Backend: Some tests failed"
fi

if [ "$FRONTEND_SUCCESS" = true ]; then
    print_success "Frontend: All tests passed"
else
    print_error "Frontend: Some tests failed"
fi

echo ""

if [ "$BACKEND_SUCCESS" = true ] && [ "$FRONTEND_SUCCESS" = true ]; then
    print_success "🎉 All tests passed! Your code is ready for deployment."
    exit 0
else
    print_error "❌ Some tests failed. Please fix the issues before proceeding."
    exit 1
fi
