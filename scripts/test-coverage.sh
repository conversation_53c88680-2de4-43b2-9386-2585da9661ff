#!/bin/bash

# Test Coverage Report Generator for Vibe Check Monorepo
# Generates coverage reports for both backend and frontend

set -e

echo "📊 Generating Test Coverage Reports"
echo "==================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Backend Coverage
print_status "Generating Backend Coverage Report..."
cd backend

# Install coverage if needed
pip install -q pytest-cov

# Run tests with coverage
pytest tests/ --cov=. --cov-report=html --cov-report=term-missing --cov-exclude="tests/*"

print_success "Backend coverage report generated in backend/htmlcov/"

echo ""

# Frontend Coverage
print_status "Generating Frontend Coverage Report..."
cd ../frontend

# Run tests with coverage
npm run test:coverage -- --watchAll=false

print_success "Frontend coverage report generated in frontend/coverage/"

cd ..

echo ""
print_success "📊 Coverage reports generated successfully!"
echo ""
echo "View reports:"
echo "  Backend:  open backend/htmlcov/index.html"
echo "  Frontend: open frontend/coverage/lcov-report/index.html"
