{"permissions": {"allow": ["Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(python -m pytest tests/ -v)", "Bash(pip install:*)", "Bash(python -m pytest tests/test_api.py::TestFlaskAPI::test_valid_analyze_request -v)", "<PERSON><PERSON>(python:*)", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git reset)", "Bash(npm test:*)", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "Bash(git stash push:*)", "Bash(git stash:*)", "Bash(git merge:*)"], "deny": []}}