# Vibe Check - AI-Powered Communication Analysis

A full-stack application that analyzes team communication for emotional tone, conflict markers, and provides actionable recommendations using AI.

## Project Structure

```
vibe-check-augment/
├── backend/                 # Flask API server
│   ├── app.py              # Main Flask application
│   ├── vibe_check_engine.py # AI-powered analysis engine
│   ├── requirements.txt    # Python dependencies
│   └── ...
├── frontend/               # React TypeScript frontend
│   ├── src/
│   │   ├── App.tsx        # Main application component
│   │   ├── index.css      # Tailwind CSS imports
│   │   └── ...
│   ├── package.json       # Node.js dependencies
│   └── ...
└── README.md              # This file
```

## Features

### Core Analysis
- **AI-Powered Analysis**: Uses OpenAI's API with Dignity Index and Nonviolent Communication frameworks
- **Psychological Safety Scoring**: Advanced metrics for team psychological safety and burnout risk
- **Communication Pattern Recognition**: Identifies dignity violations, communication gaps, and positive patterns
- **Flexible Input**: Manual message input or direct Slack channel analysis

### Team Dashboard (New!)
- **Multi-Team Management**: Switch between different teams and workspaces
- **Historical Trends**: Interactive charts showing communication health over time
- **Channel Comparison**: Side-by-side analysis of all team channels
- **Team Member Insights**: Individual participation and communication health metrics
- **Real-time Metrics**: Team health scores, dignity trends, and safety indicators

### Integrations
- **Slack Integration**: Direct analysis of Slack channels with real-time data retrieval
- **Channel Discovery**: Browse and analyze any accessible Slack channel
- **Metadata Enrichment**: User information, message counts, and participation stats

### Security & Reliability
- **Input Validation**: Comprehensive request validation with detailed error messages
- **Rate Limiting**: API protection against abuse and DoS attacks
- **Secure Logging**: Sensitive data sanitization in all logs
- **CORS Security**: Configurable origin restrictions
- **Error Handling**: Graceful degradation with meaningful user feedback

## Quick Start

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Install Python dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env file with your API keys:
# - OPENAI_API_KEY: Your OpenAI API key
# - SLACK_BOT_TOKEN: Your Slack Bot Token (optional, for Slack integration)
```

4. Start the Flask server:
```bash
python app.py
```

The backend will be available at `http://localhost:5008`

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd frontend
```

2. Install Node.js dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

## Usage

### Manual Analysis
1. Open the frontend in your browser
2. Select "📝 Manual Input" mode
3. Enter messages either as:
   - JSON array: `["Hello team!", "I have concerns about this approach"]`
   - Line-separated text (one message per line)
4. Click "Analyze Messages"

### Slack Integration
1. Set up Slack Bot Token (see Slack Setup section below)
2. Select "💬 Slack Channel" mode
3. Choose a channel from the dropdown
4. Select time range (1-30 days)
5. Click "Analyze Slack Channel"

### Team Dashboard Usage

The main dashboard (/) provides a comprehensive overview of team communication health:

1. **Team Selection**: Use the dropdown to switch between different teams
2. **Time Range**: Select 7, 14, or 30 days of historical data
3. **Key Metrics**: Monitor team health, dignity scores, psychological safety, and total analyses
4. **Trend Analysis**: View communication trends over time with interactive charts
5. **Channel Comparison**: Compare health metrics across all team channels
6. **Member Insights**: Individual team member communication patterns and concerns
7. **Recent Activity**: Latest analysis results with quick health assessments

**Navigation**: 
- Dashboard (🏠): Team overview and metrics
- Single Analysis (💬): Analyze individual messages or channels

### Analysis Results
View comprehensive results including:
- **Dignity Index Score**: Communication respect level (-10 to +10)
- **Psychological Safety Score**: Team safety indicator (0-100%)
- **Burnout Risk Level**: Risk assessment (low/medium/high)
- **Communication Patterns**: Dignity violations, gaps, and strengths
- **Actionable Recommendations**: AI-powered suggestions for improvement

## Slack Setup

1. Go to [Slack API Apps](https://api.slack.com/apps)
2. Create a new app or select existing app
3. Navigate to "OAuth & Permissions"
4. Add the following bot scopes:
   - `channels:read` - View basic info about public channels
   - `channels:history` - View messages in public channels
   - `groups:read` - View basic info about private channels  
   - `groups:history` - View messages in private channels
5. Install the app to your workspace
6. Copy the "Bot User OAuth Token" (starts with `xoxb-`)
7. Add it to your `.env` file as `SLACK_BOT_TOKEN`

## API Endpoints

### POST /api/v1/analyze

Analyzes an array of messages and returns communication insights.

**Request Body:**
```json
["message1", "message2", "message3"]
```

**Response:**
```json
{
  "summary": "Overall assessment of communication",
  "tone": "harmony|neutral|tension",
  "dignity_score": 4.2,
  "psychological_safety_score": 75,
  "burnout_risk_level": "low",
  "warnings": ["List of potential issues"],
  "recommendations": ["List of suggestions"],
  "patterns": {
    "dignity_violations": ["Specific violations found"],
    "communication_gaps": ["Areas for improvement"],
    "positive_patterns": ["Healthy communication patterns"]
  },
  "basic_analysis": {
    "tension_score": 0,
    "harmony_score": 5,
    "message_count": 3
  }
}
```

### Slack Integration Endpoints

#### GET /api/v1/slack/channels
Get list of available Slack channels.

**Response:**
```json
{
  "channels": [
    {
      "id": "C1234567890",
      "name": "general",
      "is_private": false,
      "member_count": 25,
      "purpose": "Company-wide announcements",
      "topic": "General discussions"
    }
  ],
  "count": 1
}
```

#### POST /api/v1/slack/analyze-channel
Analyze messages from a specific Slack channel.

**Request Body:**
```json
{
  "channel_id": "C1234567890",
  "days_back": 7,
  "limit": 100
}
```

**Response:**
Similar to `/api/v1/analyze` but includes additional `slack_metadata`:
```json
{
  "...": "standard analysis fields",
  "slack_metadata": {
    "channel_id": "C1234567890",
    "channel_name": "general",
    "date_range": "7 days",
    "total_messages": 150,
    "filtered_messages": 89,
    "unique_users": 12,
    "users": ["U123", "U456", "U789"]
  }
}
```

#### GET /api/v1/slack/test-connection
Test Slack API connection.

**Response:**
```json
{
  "status": "connected",
  "channel_count": 15,
  "message": "Slack connection successful"
}
```

## Technologies Used

### Backend
- **Flask**: Web framework with comprehensive error handling
- **OpenAI API**: AI-powered analysis with advanced frameworks
- **Slack SDK**: Real-time Slack workspace integration
- **Flask-CORS**: Configurable cross-origin resource sharing
- **Flask-Limiter**: Rate limiting and API protection
- **Marshmallow**: Input validation and serialization
- **Python**: Programming language

### Frontend
- **React 19**: UI framework with modern hooks
- **TypeScript**: Type-safe JavaScript development
- **Tailwind CSS**: Utility-first CSS framework
- **Recharts**: Professional data visualization library
- **Lucide React**: Beautiful, customizable icon library
- **Date-fns**: Modern date utility library
- **Vite**: Fast build tool and development server

### Development & Testing
- **Jest**: JavaScript testing framework
- **React Testing Library**: Component testing utilities
- **Pytest**: Python testing framework
- **MSW**: Mock Service Worker for API testing
- **ESLint**: Code linting and quality assurance

## Development

### Backend Development
- The main Flask app is in `backend/app.py`
- AI analysis logic is in `backend/vibe_check_engine.py`
- Add new dependencies to `backend/requirements.txt`

### Frontend Development
- Main component is in `frontend/src/App.tsx`
- Styling uses Tailwind CSS classes
- Add new dependencies with `npm install <package>`

## Environment Variables

### Required
- `OPENAI_API_KEY`: Required for AI analysis functionality

### Optional
- `SLACK_BOT_TOKEN`: Enables Slack workspace integration
- `DEBUG_PROMPT`: Set to "true" for detailed API logging

### Security Configuration
- `ALLOWED_ORIGINS`: Comma-separated list of allowed CORS origins (default: `http://localhost:5173`)
- `RATE_LIMIT_DEFAULT`: Default rate limit (default: `100 per hour`)
- `RATE_LIMIT_ANALYZE`: Rate limit for analysis endpoints (default: `10 per minute`)
- `RATE_LIMIT_SLACK_ANALYZE`: Rate limit for Slack analysis (default: `5 per minute`)

Example `.env` file:
```
OPENAI_API_KEY=sk-your-openai-key-here
SLACK_BOT_TOKEN=xoxb-your-slack-token-here
DEBUG_PROMPT=false
ALLOWED_ORIGINS=http://localhost:5173,https://yourdomain.com
RATE_LIMIT_DEFAULT=200 per hour
RATE_LIMIT_ANALYZE=20 per minute
```

## Troubleshooting

### Common Issues

#### Slack Integration Problems
- **"Slack authentication failed"**: Verify your `SLACK_BOT_TOKEN` in the backend `.env` file
- **"Insufficient Slack permissions"**: Ensure your bot has `channels:read` and `channels:history` scopes
- **"Cannot access this Slack channel"**: The bot may not be added to the channel or it may be private
- **"Rate limited by Slack"**: Wait a few minutes before retrying, or reduce the analysis scope

#### Backend Connection Issues
- **"Cannot connect to backend server"**: Ensure the backend is running on `http://localhost:5008`
- **"Request timed out"**: The backend may be overloaded; try again with a smaller dataset
- **"Server error during analysis"**: Check backend logs for OpenAI API issues or rate limits

#### Frontend Issues
- **Dashboard components not loading**: Check browser console for errors and refresh the page
- **Charts not displaying**: Ensure your browser supports modern JavaScript features
- **Navigation not working**: Clear browser cache and reload the page

#### Performance Issues
- **Slow analysis**: Large Slack channels with many messages take longer; try reducing the time range
- **High memory usage**: Analyzing 30 days of data from busy channels requires significant resources
- **Timeouts**: The application has built-in timeouts; large analyses may need to be broken down

### Getting Help
- Check browser developer console for detailed error messages
- Review backend logs for API-related issues
- Ensure all environment variables are properly configured
- Verify your OpenAI API key has sufficient credits and permissions

### Testing Error Handling
The application includes comprehensive error boundaries and timeout handling. To test:
1. Disconnect from the internet and try refreshing the dashboard
2. Use an invalid Slack token to test authentication errors
3. Try analyzing a very large channel to test timeout handling

## License

This project is part of the Vibe Check application suite.
