import { useState } from 'react'

interface AnalysisResult {
  summary: string
  tone: 'harmony' | 'neutral' | 'tension'
  dignity_score: number
  psychological_safety_score: number
  burnout_risk_level: 'low' | 'medium' | 'high'
  warnings: string[]
  recommendations: string[]
  patterns: {
    dignity_violations: string[]
    communication_gaps: string[]
    positive_patterns: string[]
  }
  slack_metadata?: {
    channel_id: string
    channel_name: string
    date_range: string
    total_messages: number
    filtered_messages: number
    unique_users: number
    users: string[]
  }
  basic_analysis?: {
    tension_score: number
    harmony_score: number
    message_count: number
  }
}

interface SlackChannel {
  id: string
  name: string
  is_private: boolean
  member_count: number
  purpose: string
  topic: string
}

function AnalysisPage() {
  const [messages, setMessages] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  
  // Slack integration state
  const [analysisMode, setAnalysisMode] = useState<'manual' | 'slack'>('manual')
  const [slackChannels, setSlackChannels] = useState<SlackChannel[]>([])
  const [selectedChannel, setSelectedChannel] = useState<string>('')
  const [daysBack, setDaysBack] = useState<number>(7)
  const [slackConnected, setSlackConnected] = useState<boolean | null>(null)

  const parseMessages = (input: string): string[] => {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(input)
      if (Array.isArray(parsed)) {
        return parsed.filter(item => typeof item === 'string')
      }
      throw new Error('Not an array')
    } catch {
      // If JSON parsing fails, split by lines and filter empty lines
      return input
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
    }
  }

  const testSlackConnection = async () => {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout
      
      const response = await fetch('http://localhost:5008/api/v1/slack/test-connection', {
        signal: controller.signal
      })
      clearTimeout(timeoutId)
      
      const data = await response.json()
      
      if (response.ok) {
        setSlackConnected(true)
        await loadSlackChannels()
      } else {
        setSlackConnected(false)
        // Handle specific Slack API errors
        if (response.status === 401) {
          setError('Slack authentication failed. Please check your bot token in the backend configuration.')
        } else if (response.status === 403) {
          setError('Slack bot lacks required permissions. Please ensure your bot has channels:read and channels:history scopes.')
        } else if (response.status === 429) {
          setError('Rate limited by Slack API. Please wait a moment and try again.')
        } else {
          setError(data.message || `Slack connection failed (${response.status}). Please check your backend configuration.`)
        }
      }
    } catch (error) {
      setSlackConnected(false)
      console.error('Slack connection test failed:', error)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          setError('Slack connection test timed out. The backend server may be unavailable.')
        } else if (error.message.includes('fetch')) {
          setError('Cannot connect to backend server. Please ensure the backend is running on http://localhost:5008')
        } else {
          setError(`Slack connection failed: ${error.message}`)
        }
      } else {
        setError('An unexpected error occurred while testing Slack connection.')
      }
    }
  }

  const loadSlackChannels = async () => {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 8000) // 8 second timeout
      
      const response = await fetch('http://localhost:5008/api/v1/slack/channels', {
        signal: controller.signal
      })
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        
        if (response.status === 401) {
          throw new Error('Slack authentication failed. Please verify your bot token.')
        } else if (response.status === 403) {
          throw new Error('Insufficient Slack permissions. Your bot needs channels:read scope.')
        } else if (response.status === 429) {
          throw new Error('Rate limited by Slack. Please wait a moment before retrying.')
        } else {
          throw new Error(errorData.error || `Failed to load channels (HTTP ${response.status})`)
        }
      }

      const data = await response.json()
      if (!data.channels || !Array.isArray(data.channels)) {
        throw new Error('Invalid response format from Slack API')
      }
      
      setSlackChannels(data.channels)
    } catch (error) {
      console.error('Failed to load Slack channels:', error)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          setError('Request timed out while loading Slack channels. Please try again.')
        } else {
          setError(error.message)
        }
      } else {
        setError('An unexpected error occurred while loading Slack channels.')
      }
    }
  }

  const analyzeSlackChannel = async () => {
    if (!selectedChannel) {
      setError('Please select a Slack channel to analyze')
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout for analysis
      
      const response = await fetch('http://localhost:5008/api/v1/slack/analyze-channel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channel_id: selectedChannel,
          days_back: daysBack,
          limit: 100
        }),
        signal: controller.signal
      })
      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        
        if (response.status === 400) {
          throw new Error(errorData.error || 'Invalid request parameters. Please check your selection and try again.')
        } else if (response.status === 401) {
          throw new Error('Slack authentication failed during analysis. Please reconnect to Slack.')
        } else if (response.status === 403) {
          throw new Error('Cannot access this Slack channel. The bot may not have permission or the channel may be private.')
        } else if (response.status === 404) {
          throw new Error('Slack channel not found. It may have been deleted or renamed.')
        } else if (response.status === 429) {
          throw new Error('Rate limited by Slack or OpenAI API. Please wait a few minutes and try again.')
        } else if (response.status === 500) {
          throw new Error('Server error during analysis. Please try again or contact support if the issue persists.')
        } else {
          throw new Error(errorData.error || `Analysis failed (HTTP ${response.status}). Please try again.`)
        }
      }

      const data: AnalysisResult = await response.json()
      
      // Validate response structure
      if (!data || typeof data.summary !== 'string') {
        throw new Error('Invalid analysis response format received from server.')
      }
      
      setResult(data)
    } catch (error) {
      console.error('Slack channel analysis failed:', error)
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          setError('Analysis timed out. Large channels may take longer to process. Please try with a shorter time range.')
        } else {
          setError(error.message)
        }
      } else {
        setError('An unexpected error occurred during Slack channel analysis.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const analyzeMessages = async () => {
    if (analysisMode === 'slack') {
      return analyzeSlackChannel()
    }

    if (!messages.trim()) {
      setError('Please enter some messages to analyze')
      return
    }

    setIsLoading(true)
    setError(null)
    setResult(null)

    try {
      const messageArray = parseMessages(messages)

      if (messageArray.length === 0) {
        setError('No valid messages found. Please enter messages as JSON array or line-separated text.')
        return
      }

      const response = await fetch('http://localhost:5008/api/v1/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageArray),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const data: AnalysisResult = await response.json()
      setResult(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const getToneColor = (tone: string) => {
    switch (tone) {
      case 'harmony':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'tension':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  const getToneIcon = (tone: string) => {
    switch (tone) {
      case 'harmony':
        return '✅'
      case 'tension':
        return '⚠️'
      default:
        return 'ℹ️'
    }
  }

  const getBurnoutRiskColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    }
  }

  const getDignityScoreColor = (score: number) => {
    if (score >= 5) return 'text-green-600'
    if (score >= 0) return 'text-blue-600'
    if (score >= -5) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getPsychologicalSafetyColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-blue-600'
    if (score >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Single Analysis
          </h1>
          <p className="text-lg text-gray-600">
            Analyze individual messages or Slack channels
          </p>
        </div>

        {/* Analysis Mode Selector */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Analysis Source</h2>
          <div className="flex space-x-4 mb-6">
            <button
              onClick={() => setAnalysisMode('manual')}
              className={`px-4 py-2 rounded-md border ${
                analysisMode === 'manual'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              📝 Manual Input
            </button>
            <button
              onClick={() => {
                setAnalysisMode('slack')
                if (slackConnected === null) {
                  testSlackConnection()
                }
              }}
              className={`px-4 py-2 rounded-md border ${
                analysisMode === 'slack'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              💬 Slack Channel
            </button>
          </div>

          {analysisMode === 'manual' && (
            <>
              <label htmlFor="messages" className="block text-sm font-medium text-gray-700 mb-2">
                Messages to Analyze
              </label>
              <p className="text-sm text-gray-500 mb-4">
                Enter messages as a JSON array (e.g., ["Hello", "How are you?"]) or paste them line by line
              </p>
              <textarea
                id="messages"
                value={messages}
                onChange={(e) => setMessages(e.target.value)}
                placeholder={`Enter messages as JSON array:\n["Great work team!", "I have some concerns about this approach"]\n\nOr line by line:\nGreat work team!\nI have some concerns about this approach`}
                className="w-full h-40 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                disabled={isLoading}
              />
            </>
          )}

          {analysisMode === 'slack' && (
            <>
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Slack Integration Status</h3>
                {slackConnected === null && (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                    <span className="text-sm text-gray-600">Testing connection...</span>
                  </div>
                )}
                {slackConnected === true && (
                  <div className="flex items-center space-x-2 text-green-600">
                    <span>✅</span>
                    <span className="text-sm">Connected to Slack ({slackChannels.length} channels available)</span>
                  </div>
                )}
                {slackConnected === false && (
                  <div className="flex items-center space-x-2 text-red-600">
                    <span>❌</span>
                    <span className="text-sm">Slack not connected</span>
                    <button
                      onClick={testSlackConnection}
                      className="ml-2 text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200"
                    >
                      Retry
                    </button>
                  </div>
                )}
              </div>

              {slackConnected && (
                <>
                  <div className="mb-4">
                    <label htmlFor="channel-select" className="block text-sm font-medium text-gray-700 mb-2">
                      Select Channel
                    </label>
                    <select
                      id="channel-select"
                      value={selectedChannel}
                      onChange={(e) => setSelectedChannel(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      disabled={isLoading}
                    >
                      <option value="">Choose a channel...</option>
                      {slackChannels.map((channel) => (
                        <option key={channel.id} value={channel.id}>
                          #{channel.name} ({channel.member_count} members)
                          {channel.is_private ? ' 🔒' : ''}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="days-back" className="block text-sm font-medium text-gray-700 mb-2">
                      Days to Analyze
                    </label>
                    <select
                      id="days-back"
                      value={daysBack}
                      onChange={(e) => setDaysBack(Number(e.target.value))}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      disabled={isLoading}
                    >
                      <option value={1}>Last 1 day</option>
                      <option value={3}>Last 3 days</option>
                      <option value={7}>Last 7 days</option>
                      <option value={14}>Last 14 days</option>
                      <option value={30}>Last 30 days</option>
                    </select>
                  </div>
                </>
              )}
            </>
          )}

          <button
            onClick={analyzeMessages}
            disabled={
              isLoading || 
              (analysisMode === 'manual' && !messages.trim()) ||
              (analysisMode === 'slack' && (!slackConnected || !selectedChannel))
            }
            className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Analyzing...
              </span>
            ) : (
              analysisMode === 'slack' ? 'Analyze Slack Channel' : 'Analyze Messages'
            )}
          </button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">❌</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {result && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Analysis Results</h2>

            {/* Status Badges */}
            <div className="mb-6 flex flex-wrap gap-3">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getToneColor(result.tone)}`}>
                <span className="mr-2">{getToneIcon(result.tone)}</span>
                Tone: {result.tone.charAt(0).toUpperCase() + result.tone.slice(1)}
              </div>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getBurnoutRiskColor(result.burnout_risk_level)}`}>
                <span className="mr-2">🔥</span>
                Burnout Risk: {result.burnout_risk_level.charAt(0).toUpperCase() + result.burnout_risk_level.slice(1)}
              </div>
            </div>

            {/* Key Metrics */}
            <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Dignity Index Score</h4>
                <div className={`text-2xl font-bold ${getDignityScoreColor(result.dignity_score)}`}>
                  {result.dignity_score > 0 ? '+' : ''}{result.dignity_score.toFixed(1)}
                  <span className="text-sm text-gray-500 ml-1">/ 10</span>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {result.dignity_score >= 5 ? 'Strong dignity affirmation' : 
                   result.dignity_score >= 0 ? 'Respectful communication' :
                   result.dignity_score >= -5 ? 'Some dignity concerns' : 'Dignity violations detected'}
                </p>
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Psychological Safety</h4>
                <div className={`text-2xl font-bold ${getPsychologicalSafetyColor(result.psychological_safety_score)}`}>
                  {Math.round(result.psychological_safety_score)}%
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {result.psychological_safety_score >= 80 ? 'Very safe environment' :
                   result.psychological_safety_score >= 60 ? 'Generally safe' :
                   result.psychological_safety_score >= 40 ? 'Some safety concerns' : 'Low psychological safety'}
                </p>
              </div>
            </div>

            {/* Summary */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Summary</h3>
              <p className="text-gray-700 leading-relaxed">{result.summary}</p>
            </div>

            {/* Warnings */}
            {result.warnings.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">⚠️ Potential Issues</h3>
                <ul className="space-y-2">
                  {result.warnings.map((warning, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-yellow-500 mr-2 mt-0.5">•</span>
                      <span className="text-gray-700">{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Recommendations */}
            {result.recommendations.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">💡 Recommendations</h3>
                <ul className="space-y-2">
                  {result.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-green-500 mr-2 mt-0.5">•</span>
                      <span className="text-gray-700">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Communication Patterns Analysis */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">🔍 Communication Patterns</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Dignity Violations */}
                {result.patterns.dignity_violations.length > 0 && (
                  <div className="bg-red-50 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-red-800 mb-2">⚠️ Dignity Concerns</h4>
                    <ul className="space-y-1">
                      {result.patterns.dignity_violations.map((violation, index) => (
                        <li key={index} className="text-xs text-red-700">• {violation}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Communication Gaps */}
                {result.patterns.communication_gaps.length > 0 && (
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-yellow-800 mb-2">🔧 Areas for Improvement</h4>
                    <ul className="space-y-1">
                      {result.patterns.communication_gaps.map((gap, index) => (
                        <li key={index} className="text-xs text-yellow-700">• {gap}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Positive Patterns */}
                {result.patterns.positive_patterns.length > 0 && (
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-green-800 mb-2">✅ Strengths</h4>
                    <ul className="space-y-1">
                      {result.patterns.positive_patterns.map((pattern, index) => (
                        <li key={index} className="text-xs text-green-700">• {pattern}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {/* Slack Metadata */}
            {result.slack_metadata && (
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">💬 Slack Channel Info</h3>
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <h4 className="text-sm font-semibold text-blue-800 mb-1">Channel</h4>
                      <p className="text-sm text-blue-700">#{result.slack_metadata.channel_name}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-blue-800 mb-1">Time Range</h4>
                      <p className="text-sm text-blue-700">{result.slack_metadata.date_range}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-blue-800 mb-1">Messages</h4>
                      <p className="text-sm text-blue-700">
                        {result.slack_metadata.filtered_messages} analyzed / {result.slack_metadata.total_messages} total
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-blue-800 mb-1">Participants</h4>
                      <p className="text-sm text-blue-700">{result.slack_metadata.unique_users} users</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Basic Analysis Stats (Legacy) */}
            {result.basic_analysis && (
              <div className="border-t pt-4 mt-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">📊 Legacy Metrics</h3>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="text-2xl font-bold text-gray-900">{result.basic_analysis.message_count}</div>
                    <div className="text-sm text-gray-600">Messages</div>
                  </div>
                  <div className="bg-red-50 rounded-lg p-3">
                    <div className="text-2xl font-bold text-red-600">{result.basic_analysis.tension_score}</div>
                    <div className="text-sm text-gray-600">Tension Score</div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="text-2xl font-bold text-green-600">{result.basic_analysis.harmony_score}</div>
                    <div className="text-sm text-gray-600">Harmony Score</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default AnalysisPage