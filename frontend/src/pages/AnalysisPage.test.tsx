import { render, screen } from '../test-utils'
import AnalysisPage from './AnalysisPage'

// Mock fetch for API calls
beforeEach(() => {
  global.fetch = jest.fn()
})

afterEach(() => {
  jest.restoreAllMocks()
})

describe('AnalysisPage', () => {
  it('renders page title and description', () => {
    render(<AnalysisPage />)
    
    expect(screen.getByText('Single Analysis')).toBeInTheDocument()
    expect(screen.getByText('Analyze individual messages or Slack channels')).toBeInTheDocument()
  })

  it('renders analysis mode selector', () => {
    render(<AnalysisPage />)
    
    expect(screen.getByText('📝 Manual Input')).toBeInTheDocument()
    expect(screen.getByText('💬 Slack Channel')).toBeInTheDocument()
  })

  it('shows manual input mode by default', () => {
    render(<AnalysisPage />)
    
    expect(screen.getByText('Messages to Analyze')).toBeInTheDocument()
    expect(screen.getByPlaceholderText(/Enter messages as JSON array/)).toBeInTheDocument()
  })

  it('shows analyze button', () => {
    render(<AnalysisPage />)
    
    const analyzeButton = screen.getByRole('button', { name: /Analyze Messages/i })
    expect(analyzeButton).toBeInTheDocument()
    expect(analyzeButton).toBeDisabled() // Should be disabled with no input
  })

  it('renders without router errors', () => {
    // This test ensures the component works within router context
    const { container } = render(<AnalysisPage />)
    expect(container.firstChild).toBeInTheDocument()
  })
})