import '@testing-library/jest-dom';

// Polyfill for React Router if needed  
if (typeof global.TextEncoder === 'undefined') {
  // @ts-ignore - Simple polyfill for testing
  global.TextEncoder = class {
    encode(str: string): Uint8Array {
      return new Uint8Array(Buffer.from(str, 'utf8'));
    }
  };
  // @ts-ignore - Simple polyfill for testing  
  global.TextDecoder = class {
    decode(data: Uint8Array): string {
      return Buffer.from(data).toString('utf8');
    }
  };
}

// Mock fetch globally for all tests
global.fetch = jest.fn();

// Reset fetch mock before each test
beforeEach(() => {
  (fetch as jest.Mock).mockClear();
});

// Mock window.matchMedia for responsive design tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
