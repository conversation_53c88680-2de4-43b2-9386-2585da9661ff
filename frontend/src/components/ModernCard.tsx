import React from 'react'

interface ModernCardProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'gradient' | 'glass'
}

const ModernCard: React.FC<ModernCardProps> = ({ 
  children, 
  className = '', 
  variant = 'default' 
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'gradient':
        return 'bg-gradient-to-br from-white via-blue-50/50 to-indigo-100/30 border-white/50 shadow-xl shadow-blue-500/10'
      case 'glass':
        return 'bg-white/80 backdrop-blur-sm border-white/30 shadow-xl shadow-black/5'
      default:
        return 'bg-white border-gray-200 shadow-lg'
    }
  }

  return (
    <div className={`
      rounded-2xl border p-6 transition-all duration-500 ease-out
      hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02]
      group cursor-pointer
      ${getVariantClasses()}
      ${className}
    `}>
      <div className="transition-all duration-300 group-hover:scale-[1.02]">
        {children}
      </div>
    </div>
  )
}

export default ModernCard