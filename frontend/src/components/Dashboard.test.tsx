import { render, screen } from '@testing-library/react'
import Dashboard from './Dashboard'

// Mock all child components to keep test simple
jest.mock('./TeamSelector', () => {
  return function MockTeamSelector() {
    return <div data-testid="team-selector">Team Selector</div>
  }
})

jest.mock('./MetricsCard', () => {
  return function MockMetricsCard({ title }: { title: string }) {
    return <div data-testid="metrics-card">{title}</div>
  }
})

jest.mock('./TrendChart', () => {
  return function MockTrendChart() {
    return <div data-testid="trend-chart">Trend Chart</div>
  }
})

jest.mock('./ChannelComparison', () => {
  return function MockChannelComparison() {
    return <div data-testid="channel-comparison">Channel Comparison</div>
  }
})

jest.mock('./TeamMemberInsights', () => {
  return function MockTeamMemberInsights() {
    return <div data-testid="team-member-insights">Team Member Insights</div>
  }
})

jest.mock('./RecentAnalyses', () => {
  return function MockRecentAnalyses() {
    return <div data-testid="recent-analyses">Recent Analyses</div>
  }
})

describe('Dashboard', () => {
  it('renders main dashboard elements', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Team Dashboard')).toBeInTheDocument()
    expect(screen.getByTestId('team-selector')).toBeInTheDocument()
    expect(screen.getByText('Refresh')).toBeInTheDocument()
  })

  it('renders team information when team is selected', () => {
    render(<Dashboard />)
    
    // Should show the first team from mock data
    expect(screen.getByText('Engineering Team')).toBeInTheDocument()
    expect(screen.getByText('Backend and Frontend Development')).toBeInTheDocument()
  })

  it('renders all metrics cards', () => {
    render(<Dashboard />)
    
    const metricsCards = screen.getAllByTestId('metrics-card')
    expect(metricsCards).toHaveLength(4) // Team Health, Dignity, Safety, Analyses
  })

  it('renders all dashboard components', () => {
    render(<Dashboard />)
    
    expect(screen.getByTestId('trend-chart')).toBeInTheDocument()
    expect(screen.getByTestId('channel-comparison')).toBeInTheDocument()
    expect(screen.getByTestId('team-member-insights')).toBeInTheDocument()
    expect(screen.getByTestId('recent-analyses')).toBeInTheDocument()
  })

  it('shows date range selector', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Range:')).toBeInTheDocument()
    expect(screen.getByText('Last 7 days')).toBeInTheDocument()
    expect(screen.getByText('Last 14 days')).toBeInTheDocument()
    expect(screen.getByText('Last 30 days')).toBeInTheDocument()
  })
})