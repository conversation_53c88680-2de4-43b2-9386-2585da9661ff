import React from 'react'
import { ChevronDown } from 'lucide-react'

interface Team {
  id: string
  name: string
  description: string
  channels: string[]
  members: number
  created_at: string
}

interface TeamSelectorProps {
  teams: Team[]
  currentTeam: Team | null
  onTeamChange: (team: Team) => void
}

const TeamSelector: React.FC<TeamSelectorProps> = ({ teams, currentTeam, onTeamChange }) => {
  return (
    <div className="relative">
      <select
        value={currentTeam?.id || ''}
        onChange={(e) => {
          const team = teams.find(t => t.id === e.target.value)
          if (team) onTeamChange(team)
        }}
        className="appearance-none bg-white border border-gray-300 rounded-md pl-3 pr-10 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-48"
      >
        <option value="">Select a team...</option>
        {teams.map((team) => (
          <option key={team.id} value={team.id}>
            {team.name} ({team.members} members)
          </option>
        ))}
      </select>
      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <ChevronDown className="h-4 w-4 text-gray-400" />
      </div>
    </div>
  )
}

export default TeamSelector