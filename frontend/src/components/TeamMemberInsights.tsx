import React from 'react'
import { MessageCircle, AlertTriangle } from 'lucide-react'

interface TeamMember {
  id: string
  name: string
  avatar_url?: string
  message_count: number
  avg_dignity_score: number
  avg_psychological_safety: number
  participation_rate: number
  recent_activity: 'high' | 'medium' | 'low'
  concerns: string[]
}

interface TeamMemberInsightsProps {
  teamId: string
}

const TeamMemberInsights: React.FC<TeamMemberInsightsProps> = () => {
  // Mock team member data - in real app this would come from API
  const teamMembers: TeamMember[] = [
    {
      id: 'member-1',
      name: '<PERSON>',
      message_count: 127,
      avg_dignity_score: 8.2,
      avg_psychological_safety: 88,
      participation_rate: 92,
      recent_activity: 'high',
      concerns: []
    },
    {
      id: 'member-2',
      name: '<PERSON>',
      message_count: 89,
      avg_dignity_score: 7.1,
      avg_psychological_safety: 75,
      participation_rate: 68,
      recent_activity: 'medium',
      concerns: ['Lower participation in recent days']
    },
    {
      id: 'member-3',
      name: '<PERSON>',
      message_count: 156,
      avg_dignity_score: 6.8,
      avg_psychological_safety: 82,
      participation_rate: 85,
      recent_activity: 'high',
      concerns: []
    },
    {
      id: 'member-4',
      name: '<PERSON>',
      message_count: 43,
      avg_dignity_score: 7.5,
      avg_psychological_safety: 70,
      participation_rate: 45,
      recent_activity: 'low',
      concerns: ['Low participation', 'May be experiencing burnout']
    },
    {
      id: 'member-5',
      name: '<PERSON>',
      message_count: 98,
      avg_dignity_score: 8.7,
      avg_psychological_safety: 91,
      participation_rate: 78,
      recent_activity: 'medium',
      concerns: []
    }
  ]

  const getActivityColor = (activity: string) => {
    switch (activity) {
      case 'high':
        return 'text-green-600 bg-green-50'
      case 'medium':
        return 'text-yellow-600 bg-yellow-50'
      case 'low':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getParticipationColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600'
    if (rate >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getDignityScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600'
    if (score >= 6) return 'text-blue-600'
    if (score >= 4) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
  }

  const sortedMembers = teamMembers.sort((a, b) => {
    // Sort by concerns first, then by participation rate
    if (a.concerns.length !== b.concerns.length) {
      return b.concerns.length - a.concerns.length
    }
    return b.participation_rate - a.participation_rate
  })

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Team Member Insights</h3>
        <p className="text-sm text-gray-500">Individual communication patterns and engagement</p>
      </div>

      <div className="space-y-4">
        {sortedMembers.map((member) => (
          <div key={member.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                {/* Avatar */}
                <div className="flex-shrink-0">
                  {member.avatar_url ? (
                    <img
                      className="h-10 w-10 rounded-full"
                      src={member.avatar_url}
                      alt={member.name}
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                      <span className="text-white text-sm font-medium">
                        {getInitials(member.name)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Member Info */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900">{member.name}</h4>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="flex items-center text-xs text-gray-500">
                      <MessageCircle className="h-3 w-3 mr-1" />
                      {member.message_count} messages
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getActivityColor(member.recent_activity)}`}>
                      {member.recent_activity} activity
                    </span>
                  </div>
                </div>
              </div>

              {/* Concerns */}
              {member.concerns.length > 0 && (
                <div className="flex items-center text-red-500">
                  <AlertTriangle className="h-4 w-4" />
                </div>
              )}
            </div>

            {/* Metrics */}
            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className={`text-lg font-semibold ${getParticipationColor(member.participation_rate)}`}>
                  {member.participation_rate}%
                </div>
                <div className="text-xs text-gray-500">Participation</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${getDignityScoreColor(member.avg_dignity_score)}`}>
                  {member.avg_dignity_score.toFixed(1)}
                </div>
                <div className="text-xs text-gray-500">Dignity Score</div>
              </div>
              <div className="text-center">
                <div className={`text-lg font-semibold ${getParticipationColor(member.avg_psychological_safety)}`}>
                  {Math.round(member.avg_psychological_safety)}%
                </div>
                <div className="text-xs text-gray-500">Safety Score</div>
              </div>
            </div>

            {/* Concerns */}
            {member.concerns.length > 0 && (
              <div className="mt-4 p-3 bg-red-50 rounded-lg">
                <h5 className="text-sm font-medium text-red-800 mb-1">Areas of Concern</h5>
                <ul className="space-y-1">
                  {member.concerns.map((concern, index) => (
                    <li key={index} className="text-sm text-red-700 flex items-start">
                      <span className="text-red-500 mr-2 mt-0.5">•</span>
                      {concern}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{teamMembers.length}</div>
            <div className="text-sm text-gray-500">Team Members</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {teamMembers.filter(m => m.concerns.length === 0).length}
            </div>
            <div className="text-sm text-gray-500">Healthy</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-yellow-600">
              {teamMembers.filter(m => m.participation_rate < 70 && m.concerns.length === 0).length}
            </div>
            <div className="text-sm text-gray-500">Low Engagement</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-red-600">
              {teamMembers.filter(m => m.concerns.length > 0).length}
            </div>
            <div className="text-sm text-gray-500">Need Attention</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TeamMemberInsights