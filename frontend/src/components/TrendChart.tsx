import React from 'react'
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts'
import { format } from 'date-fns'

interface AnalysisHistory {
  date: string
  dignity_score: number
  psychological_safety_score: number
  burnout_risk_level: 'low' | 'medium' | 'high'
  channel_name: string
  message_count: number
}

interface AggregatedData {
  date: string
  dignity_scores: number[]
  safety_scores: number[]
  message_counts: number[]
}

interface TooltipPayload {
  name: string
  value: number
  color: string
}

interface CustomTooltipProps {
  active?: boolean
  payload?: TooltipPayload[]
  label?: string
}

interface TrendChartProps {
  data: AnalysisHistory[]
}

const TrendChart: React.FC<TrendChartProps> = ({ data }) => {
  // Aggregate data by date for cleaner visualization
  const aggregatedData = data.reduce((acc, item) => {
    const date = item.date
    if (!acc[date]) {
      acc[date] = {
        date,
        dignity_scores: [],
        safety_scores: [],
        message_counts: []
      }
    }
    acc[date].dignity_scores.push(item.dignity_score)
    acc[date].safety_scores.push(item.psychological_safety_score)
    acc[date].message_counts.push(item.message_count)
    return acc
  }, {} as Record<string, AggregatedData>)

  const chartData = Object.values(aggregatedData).map((item: AggregatedData) => ({
    date: format(new Date(item.date), 'MMM dd'),
    dignity_score: item.dignity_scores.reduce((sum: number, score: number) => sum + score, 0) / item.dignity_scores.length,
    psychological_safety_score: item.safety_scores.reduce((sum: number, score: number) => sum + score, 0) / item.safety_scores.length,
    message_count: item.message_counts.reduce((sum: number, count: number) => sum + count, 0)
  }))

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: TooltipPayload, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.name.includes('score') ? entry.value.toFixed(1) : Math.round(entry.value)}
              {entry.name.includes('safety') && '%'}
              {entry.name.includes('dignity') && '/10'}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Communication Trends</h3>
        <p className="text-sm text-gray-500">Daily averages across all channels</p>
      </div>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="date" 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey="dignity_score"
              stroke="#3b82f6"
              strokeWidth={2}
              dot={{ r: 4 }}
              name="Dignity Score"
            />
            <Line
              type="monotone"
              dataKey="psychological_safety_score"
              stroke="#10b981"
              strokeWidth={2}
              dot={{ r: 4 }}
              name="Psychological Safety"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
        <span>
          Total Messages: {chartData.reduce((sum, item) => sum + item.message_count, 0)}
        </span>
        <span>
          {chartData.length} days of data
        </span>
      </div>
    </div>
  )
}

export default TrendChart