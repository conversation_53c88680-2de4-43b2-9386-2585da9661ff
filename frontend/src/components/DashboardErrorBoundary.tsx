import React from 'react'
import ErrorBoundary from './ErrorBoundary'
import { AlertTriangle, Home } from 'lucide-react'

interface DashboardErrorBoundaryProps {
  children: React.ReactNode
  componentName?: string
}

const DashboardErrorBoundary: React.FC<DashboardErrorBoundaryProps> = ({ 
  children, 
  componentName = 'Dashboard Component' 
}) => {
  const handleError = (error: Error) => {
    // Log error for monitoring/debugging
    console.error(`${componentName} Error:`, error)
    
    // In a real app, you might send this to an error reporting service
    // trackError(error, { component: componentName, context: 'dashboard' })
  }

  const fallbackUI = (
    <div className="bg-white p-6 rounded-lg shadow border-l-4 border-red-400">
      <div className="flex items-start space-x-3">
        <AlertTriangle className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-900 mb-1">
            {componentName} Unavailable
          </h3>
          <p className="text-sm text-gray-600 mb-4">
            This dashboard component couldn't load properly. The rest of your dashboard should still work normally.
          </p>
          <div className="flex space-x-3">
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Reload Page
            </button>
            <a
              href="/"
              className="inline-flex items-center px-3 py-1.5 border border-transparent rounded text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200"
            >
              <Home className="h-3 w-3 mr-1" />
              Go to Main Page
            </a>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <ErrorBoundary fallback={fallbackUI} onError={handleError}>
      {children}
    </ErrorBoundary>
  )
}

export default DashboardErrorBoundary