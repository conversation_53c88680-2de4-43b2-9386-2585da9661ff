import React, { useState, useEffect, useCallback } from 'react'
import { 
  Users, 
  TrendingUp, 
  MessageSquare, 
  AlertTriangle, 
  Calendar,
  RefreshCw
} from 'lucide-react'
import { format, subDays } from 'date-fns'
import TeamSelector from './TeamSelector'
import MetricsCard from './MetricsCard'
import <PERSON>rend<PERSON>hart from './TrendChart'
import Channel<PERSON>om<PERSON>ison from './ChannelComparison'
import TeamMemberInsights from './TeamMemberInsights'
import RecentAnalyses from './RecentAnalyses'

interface Team {
  id: string
  name: string
  description: string
  channels: string[]
  members: number
  created_at: string
}

interface DashboardMetrics {
  total_analyses: number
  avg_dignity_score: number
  avg_psychological_safety: number
  trend_direction: 'up' | 'down' | 'stable'
  active_channels: number
  team_health_score: number
}

interface AnalysisHistory {
  date: string
  dignity_score: number
  psychological_safety_score: number
  burnout_risk_level: 'low' | 'medium' | 'high'
  channel_name: string
  message_count: number
}

const Dashboard: React.FC = () => {
  const [currentTeam, setCurrentTeam] = useState<Team | null>(null)
  const [teams, setTeams] = useState<Team[]>([])
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null)
  const [analysisHistory, setAnalysisHistory] = useState<AnalysisHistory[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [dateRange] = useState(7) // Days

  const loadMockData = useCallback(() => {
    // Mock teams
    const mockTeams: Team[] = [
      {
        id: 'team-1',
        name: 'Engineering Team',
        description: 'Backend and Frontend Development',
        channels: ['general', 'backend-dev', 'frontend-dev', 'standup'],
        members: 12,
        created_at: '2024-01-15'
      },
      {
        id: 'team-2', 
        name: 'Product Team',
        description: 'Product Management and Design',
        channels: ['product-general', 'design', 'user-research'],
        members: 8,
        created_at: '2024-02-01'
      },
      {
        id: 'team-3',
        name: 'Marketing Team',
        description: 'Growth and Content Marketing',
        channels: ['marketing', 'content', 'growth-experiments'],
        members: 6,
        created_at: '2024-01-20'
      }
    ]

    setTeams(mockTeams)
    
    if (!currentTeam && mockTeams.length > 0) {
      setCurrentTeam(mockTeams[0])
    }

    // Mock metrics
    setMetrics({
      total_analyses: 47,
      avg_dignity_score: 6.8,
      avg_psychological_safety: 82,
      trend_direction: 'up',
      active_channels: currentTeam?.channels.length || 4,
      team_health_score: 85
    })

    // Mock analysis history
    const mockHistory: AnalysisHistory[] = []
    for (let i = dateRange; i >= 0; i--) {
      const date = format(subDays(new Date(), i), 'yyyy-MM-dd')
      mockHistory.push({
        date,
        dignity_score: 5 + Math.random() * 4, // 5-9 range
        psychological_safety_score: 70 + Math.random() * 25, // 70-95 range
        burnout_risk_level: Math.random() > 0.7 ? 'medium' : Math.random() > 0.9 ? 'high' : 'low',
        channel_name: currentTeam?.channels[Math.floor(Math.random() * (currentTeam?.channels.length || 1))] || 'general',
        message_count: Math.floor(20 + Math.random() * 50)
      })
    }
    setAnalysisHistory(mockHistory)
  }, [currentTeam, dateRange])

  useEffect(() => {
    loadMockData()
  }, [loadMockData])

  const handleTeamChange = (team: Team) => {
    setCurrentTeam(team)
  }

  const handleRefresh = async () => {
    setIsLoading(true)
    
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      loadMockData()
    } catch (error) {
      console.error('Dashboard refresh failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getTrendIcon = () => {
    if (!metrics) return null
    
    switch (metrics.trend_direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingUp className="h-4 w-4 text-red-500 transform rotate-180" />
      default:
        return <TrendingUp className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <div className="bg-white/90 backdrop-blur-sm shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">
                Team Dashboard
              </h1>
              {getTrendIcon()}
            </div>
            
            <div className="flex items-center space-x-4">
              <TeamSelector 
                teams={teams}
                currentTeam={currentTeam}
                onTeamChange={handleTeamChange}
              />
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Team Info */}
        {currentTeam && (
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">{currentTeam.name}</h2>
            <p className="text-gray-600">{currentTeam.description}</p>
            <div className="flex items-center space-x-6 mt-4 text-sm text-gray-500">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                {currentTeam.members} members
              </div>
              <div className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                {currentTeam.channels.length} channels
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                Since {format(new Date(currentTeam.created_at), 'MMM yyyy')}
              </div>
            </div>
          </div>
        )}

        {/* Metrics Grid */}
        {metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <MetricsCard
              title="TEAM HEALTH"
              value={`${metrics.team_health_score}%`}
              icon={<Users className="h-6 w-6" />}
              color="blue"
              trend={metrics.trend_direction}
              subtitle="Overall team wellbeing"
            />
            <MetricsCard
              title="AVG DIGNITY SCORE"
              value={metrics.avg_dignity_score.toFixed(1)}
              icon={<TrendingUp className="h-6 w-6" />}
              color="purple"
              trend={metrics.avg_dignity_score > 5 ? 'up' : 'down'}
              subtitle="Scale of -10 to +10"
            />
            <MetricsCard
              title="PSYCHOLOGICAL SAFETY"
              value={`${metrics.avg_psychological_safety}%`}
              icon={<AlertTriangle className="h-6 w-6" />}
              color="green"
              trend={metrics.avg_psychological_safety > 75 ? 'up' : 'down'}
              subtitle="Team trust level"
            />
            <MetricsCard
              title="TOTAL ANALYSES"
              value={metrics.total_analyses.toString()}
              icon={<MessageSquare className="h-6 w-6" />}
              color="indigo"
              trend="stable"
              subtitle={`${metrics.active_channels} active channels`}
            />
          </div>
        )}

        {/* Charts and Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Communication Trends</h3>
            <TrendChart data={analysisHistory} />
          </div>
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Channel Comparison</h3>
            <ChannelComparison channels={currentTeam?.channels || []} />
          </div>
        </div>

        {/* Team Member Insights */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">Team Member Insights</h3>
          <TeamMemberInsights teamId={currentTeam?.id || ''} />
        </div>

        {/* Recent Analyses */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4">Recent Analyses</h3>
          <RecentAnalyses analyses={analysisHistory.slice(0, 5)} />
        </div>
      </main>
    </div>
  )
}

export default Dashboard