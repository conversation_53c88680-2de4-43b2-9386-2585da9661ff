import { render, screen } from '@testing-library/react'
import Trend<PERSON>hart from './TrendChart'
import { mockAnalysisHistory } from '../test-utils/mockData'

// Mock Recharts components to keep tests simple
jest.mock('recharts', () => ({
  LineChart: ({ children }: { children: React.ReactNode }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-container">{children}</div>,
  Legend: () => <div data-testid="legend" />
}))

describe('Trend<PERSON>hart', () => {
  it('renders chart title and description', () => {
    render(<TrendChart data={mockAnalysisHistory} />)
    
    expect(screen.getByText('Communication Trends')).toBeInTheDocument()
    expect(screen.getByText('Daily averages across all channels')).toBeInTheDocument()
  })

  it('renders chart components', () => {
    render(<TrendChart data={mockAnalysisHistory} />)
    
    expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    expect(screen.getByTestId('x-axis')).toBeInTheDocument()
    expect(screen.getByTestId('y-axis')).toBeInTheDocument()
  })

  it('shows summary statistics', () => {
    render(<TrendChart data={mockAnalysisHistory} />)
    
    expect(screen.getByText(/Total Messages:/)).toBeInTheDocument()
    expect(screen.getByText(/days of data/)).toBeInTheDocument()
  })

  it('handles empty data gracefully', () => {
    render(<TrendChart data={[]} />)
    
    expect(screen.getByText('Communication Trends')).toBeInTheDocument()
    expect(screen.getByText('0 days of data')).toBeInTheDocument()
  })
})