import { render, screen } from '@testing-library/react'
import { Users } from 'lucide-react'
import MetricsCard from './MetricsCard'

describe('MetricsCard', () => {
  const mockProps = {
    title: 'Team Health',
    value: '85%',
    icon: <Users className="h-6 w-6" />,
    color: 'text-green-600'
  }

  it('renders title and value correctly', () => {
    render(<MetricsCard {...mockProps} />)
    
    expect(screen.getByText('Team Health')).toBeInTheDocument()
    expect(screen.getByText('85%')).toBeInTheDocument()
  })

  it('displays different trends correctly', () => {
    const { rerender } = render(<MetricsCard {...mockProps} trend="up" />)
    expect(screen.getByText('85%')).toBeInTheDocument()
    
    rerender(<MetricsCard {...mockProps} trend="down" />)
    expect(screen.getByText('85%')).toBeInTheDocument()
    
    rerender(<MetricsCard {...mockProps} trend="stable" />)
    expect(screen.getByText('85%')).toBeInTheDocument()
  })

  it('shows subtitle when provided', () => {
    render(<MetricsCard {...mockProps} subtitle="Last 7 days" />)
    
    expect(screen.getByText('Last 7 days')).toBeInTheDocument()
  })

  it('applies correct color class', () => {
    render(<MetricsCard {...mockProps} color="text-red-600" />)
    
    const valueElement = screen.getByText('85%')
    expect(valueElement).toHaveClass('text-red-600')
  })
})