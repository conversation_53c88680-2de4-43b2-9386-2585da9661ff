import React from 'react'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface MetricsCardProps {
  title: string
  value: string
  icon: React.ReactNode
  color: string
  trend?: 'up' | 'down' | 'stable'
  subtitle?: string
}

const MetricsCard: React.FC<MetricsCardProps> = ({ 
  title, 
  value, 
  icon, 
  color, 
  trend = 'stable',
  subtitle 
}) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-400" />
    }
  }


  return (
    <div className="relative bg-gradient-to-br from-white via-blue-50/60 to-purple-50/40 overflow-hidden shadow-2xl hover:shadow-purple-500/20 rounded-3xl border border-gradient-to-r from-blue-200/50 to-purple-200/50 transition-all duration-700 hover:-translate-y-4 hover:scale-[1.08] group cursor-pointer backdrop-blur-sm">
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-purple-600/5 to-indigo-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      
      {/* Glowing border effect */}
      <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-indigo-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>
      
      <div className="relative p-8">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="relative">
              {/* Icon container with multiple gradients */}
              <div className="p-5 rounded-3xl bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 shadow-2xl shadow-blue-500/40 group-hover:shadow-purple-500/60 group-hover:scale-125 transition-all duration-500 relative overflow-hidden">
                {/* Animated shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                <div className="text-white relative z-10 transform group-hover:rotate-12 transition-transform duration-300">
                  {icon}
                </div>
              </div>
              
              {/* Floating accent dot */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
            </div>
          </div>
          
          <div className="ml-6 w-0 flex-1">
            <dl>
              <dt className="text-sm font-bold text-gray-600 truncate uppercase tracking-widest mb-2 group-hover:text-gray-800 transition-colors duration-300">
                {title}
              </dt>
              <dd className="flex items-baseline">
                <div className="text-4xl font-black bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent group-hover:from-blue-600 group-hover:via-purple-600 group-hover:to-indigo-600 transition-all duration-500">
                  {value}
                </div>
                <div className="ml-3 flex items-baseline text-sm transform group-hover:scale-110 transition-transform duration-300">
                  {getTrendIcon()}
                </div>
              </dd>
              {subtitle && (
                <dd className="text-sm text-gray-600 mt-2 font-medium group-hover:text-gray-700 transition-colors duration-300">
                  {subtitle}
                </dd>
              )}
            </dl>
          </div>
        </div>
      </div>
      
      {/* Bottom accent line */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
    </div>
  )
}

export default MetricsCard