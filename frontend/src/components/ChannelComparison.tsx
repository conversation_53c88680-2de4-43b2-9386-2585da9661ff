import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts'
import { MessageSquare } from 'lucide-react'

interface ChannelComparisonProps {
  channels: string[]
}

interface TooltipPayload {
  payload: {
    dignity_score: number
    safety_score: number
    health_index: number
    members: number
    messages: number
  }
}

interface CustomTooltipProps {
  active?: boolean
  payload?: TooltipPayload[]
  label?: string
}

const ChannelComparison: React.FC<ChannelComparisonProps> = ({ channels }) => {
  // Generate mock data for channels
  const channelData = channels.map(channel => {
    const base_score = 70 + Math.random() * 25 // 70-95 range
    return {
      name: channel,
      dignity_score: 5 + Math.random() * 4, // 5-9 range
      psychological_safety: base_score,
      message_count: Math.floor(50 + Math.random() * 200),
      active_members: Math.floor(3 + Math.random() * 15)
    }
  })

  const getBarColor = (value: number, metric: 'dignity' | 'safety') => {
    if (metric === 'dignity') {
      if (value >= 7) return '#10b981' // green
      if (value >= 5) return '#3b82f6' // blue
      if (value >= 3) return '#f59e0b' // yellow
      return '#ef4444' // red
    } else {
      if (value >= 80) return '#10b981' // green
      if (value >= 60) return '#3b82f6' // blue
      if (value >= 40) return '#f59e0b' // yellow
      return '#ef4444' // red
    }
  }

  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">#{label}</p>
          <div className="space-y-1 text-sm">
            <p className="text-blue-600">
              Dignity Score: {data.dignity_score.toFixed(1)}/10
            </p>
            <p className="text-green-600">
              Psychological Safety: {Math.round(data.safety_score)}%
            </p>
            <p className="text-gray-600">
              Messages: {data.messages}
            </p>
            <p className="text-gray-600">
              Active Members: {data.members}
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  const getChannelHealthStatus = (dignity: number, safety: number) => {
    const avgScore = (dignity / 10 * 100 + safety) / 2
    if (avgScore >= 80) return { status: 'Excellent', color: 'text-green-600 bg-green-50' }
    if (avgScore >= 65) return { status: 'Good', color: 'text-blue-600 bg-blue-50' }
    if (avgScore >= 50) return { status: 'Fair', color: 'text-yellow-600 bg-yellow-50' }
    return { status: 'Needs Attention', color: 'text-red-600 bg-red-50' }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Channel Comparison</h3>
        <p className="text-sm text-gray-500">Health metrics across team channels</p>
      </div>

      {/* Chart */}
      <div className="h-64 mb-6">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={channelData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              stroke="#6b7280"
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="psychological_safety" radius={[4, 4, 0, 0]}>
              {channelData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getBarColor(entry.psychological_safety, 'safety')} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Channel List */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-900">Channel Health Summary</h4>
        <div className="grid grid-cols-1 gap-2">
          {channelData.map((channel) => {
            const health = getChannelHealthStatus(channel.dignity_score, channel.psychological_safety)
            return (
              <div key={channel.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <MessageSquare className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-900">#{channel.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${health.color}`}>
                    {health.status}
                  </span>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>{channel.active_members} members</span>
                  <span>{channel.message_count} msgs</span>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default ChannelComparison