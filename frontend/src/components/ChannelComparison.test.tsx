import { render, screen } from '@testing-library/react'
import ChannelComparison from './ChannelComparison'

// Mock Recharts components
jest.mock('recharts', () => ({
  BarChart: ({ children }: { children: React.ReactNode }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-container">{children}</div>,
  Cell: () => <div data-testid="cell" />
}))

const mockChannels = ['general', 'backend-dev', 'frontend-dev']

describe('ChannelComparison', () => {
  it('renders component title and description', () => {
    render(<ChannelComparison channels={mockChannels} />)
    
    expect(screen.getByText('Channel Comparison')).toBeInTheDocument()
    expect(screen.getByText('Health metrics across team channels')).toBeInTheDocument()
  })

  it('renders bar chart components', () => {
    render(<ChannelComparison channels={mockChannels} />)
    
    expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument()
  })

  it('displays all channels in health summary', () => {
    render(<ChannelComparison channels={mockChannels} />)
    
    expect(screen.getByText('#general')).toBeInTheDocument()
    expect(screen.getByText('#backend-dev')).toBeInTheDocument()
    expect(screen.getByText('#frontend-dev')).toBeInTheDocument()
  })

  it('shows health status indicators', () => {
    render(<ChannelComparison channels={mockChannels} />)
    
    expect(screen.getByText('Channel Health Summary')).toBeInTheDocument()
    // Should show at least one health status
    const healthStatuses = screen.getAllByText(/Excellent|Good|Fair|Needs Attention/)
    expect(healthStatuses.length).toBeGreaterThan(0)
  })

  it('displays member and message counts', () => {
    render(<ChannelComparison channels={mockChannels} />)
    
    const memberTexts = screen.getAllByText(/members/)
    const msgTexts = screen.getAllByText(/msgs/)
    
    expect(memberTexts.length).toBeGreaterThan(0)
    expect(msgTexts.length).toBeGreaterThan(0)
  })

  it('handles empty channels array', () => {
    render(<ChannelComparison channels={[]} />)
    
    expect(screen.getByText('Channel Comparison')).toBeInTheDocument()
    expect(screen.getByText('Channel Health Summary')).toBeInTheDocument()
  })
})