import { render, screen, fireEvent } from '@testing-library/react'
import TeamSelector from './TeamSelector'
import { mockTeams } from '../test-utils/mockData'

describe('TeamSelector', () => {
  const mockOnTeamChange = jest.fn()

  beforeEach(() => {
    mockOnTeamChange.mockClear()
  })

  it('renders team selection dropdown', () => {
    render(
      <TeamSelector 
        teams={mockTeams}
        currentTeam={mockTeams[0]}
        onTeamChange={mockOnTeamChange}
      />
    )
    
    expect(screen.getByDisplayValue('Engineering Team (12 members)')).toBeInTheDocument()
  })

  it('shows all teams as options', () => {
    render(
      <TeamSelector 
        teams={mockTeams}
        currentTeam={null}
        onTeamChange={mockOnTeamChange}
      />
    )
    
    expect(screen.getByText('Engineering Team (12 members)')).toBeInTheDocument()
    expect(screen.getByText('Product Team (8 members)')).toBeInTheDocument()
  })

  it('calls onTeamChange when selection changes', () => {
    render(
      <TeamSelector 
        teams={mockTeams}
        currentTeam={mockTeams[0]}
        onTeamChange={mockOnTeamChange}
      />
    )
    
    const select = screen.getByRole('combobox')
    fireEvent.change(select, { target: { value: 'team-2' } })
    
    expect(mockOnTeamChange).toHaveBeenCalledWith(mockTeams[1])
  })

  it('shows placeholder when no team selected', () => {
    render(
      <TeamSelector 
        teams={mockTeams}
        currentTeam={null}
        onTeamChange={mockOnTeamChange}
      />
    )
    
    const select = screen.getByRole('combobox')
    expect(select).toHaveValue('')
    expect(screen.getByText('Select a team...')).toBeInTheDocument()
  })
})