import { render, screen } from '@testing-library/react'
import RecentAnalyses from './RecentAnalyses'
import { mockAnalysisHistory } from '../test-utils/mockData'

describe('RecentAnalyses', () => {
  it('renders component title and description', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    expect(screen.getByText('Recent Analyses')).toBeInTheDocument()
    expect(screen.getByText('Latest communication analysis results')).toBeInTheDocument()
  })

  it('displays analysis cards', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    // Should show mock analysis data
    expect(screen.getByText('#general')).toBeInTheDocument()
    expect(screen.getByText('#backend-dev')).toBeInTheDocument()
  })

  it('shows burnout risk status indicators', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    const riskStatuses = screen.getAllByText(/risk/)
    expect(riskStatuses.length).toBeGreaterThan(0)
  })

  it('displays timestamps', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    const timeTexts = screen.getAllByText(/ago/)
    expect(timeTexts.length).toBeGreaterThan(0)
  })

  it('shows summary statistics', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    expect(screen.getByText('Total Analyses')).toBeInTheDocument()
    expect(screen.getByText('Avg Dignity')).toBeInTheDocument()
    expect(screen.getByText('Avg Safety')).toBeInTheDocument()
  })

  it('displays analysis scores', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    const dignityTexts = screen.getAllByText(/Dignity/)
    const safetyTexts = screen.getAllByText(/Safety/)
    
    expect(dignityTexts.length).toBeGreaterThan(0)
    expect(safetyTexts.length).toBeGreaterThan(0)
  })

  it('shows analysis insights', () => {
    render(<RecentAnalyses analyses={mockAnalysisHistory} />)
    
    // Look for insight text patterns in the component
    const insightTexts = screen.getAllByText(/health|communication|trends/)
    expect(insightTexts.length).toBeGreaterThan(0)
  })

  it('handles empty state gracefully', () => {
    // This test ensures component handles edge cases
    const { container } = render(<RecentAnalyses analyses={[]} />)
    expect(container.firstChild).toBeInTheDocument()
  })
})