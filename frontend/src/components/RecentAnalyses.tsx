import React from 'react'
import { Clock, MessageSquare, TrendingUp, AlertTriangle } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface AnalysisHistory {
  date: string
  dignity_score: number
  psychological_safety_score: number
  burnout_risk_level: 'low' | 'medium' | 'high'
  channel_name: string
  message_count: number
}

interface RecentAnalysesProps {
  analyses: AnalysisHistory[]
}

const RecentAnalyses: React.FC<RecentAnalysesProps> = ({ analyses }) => {
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    }
  }

  const getDignityScoreColor = (score: number) => {
    if (score >= 7) return 'text-green-600'
    if (score >= 5) return 'text-blue-600'
    if (score >= 3) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSafetyScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-blue-600'
    if (score >= 40) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getAnalysisIcon = (dignityScore: number, safetyScore: number, riskLevel: string) => {
    if (riskLevel === 'high' || dignityScore < 3 || safetyScore < 40) {
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    }
    if (dignityScore >= 7 && safetyScore >= 80) {
      return <TrendingUp className="h-4 w-4 text-green-500" />
    }
    return <MessageSquare className="h-4 w-4 text-blue-500" />
  }

  // Sort analyses by date (most recent first)
  const sortedAnalyses = [...analyses].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  )

  if (analyses.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="mb-4">
          <h3 className="text-lg font-medium text-gray-900">Recent Analyses</h3>
          <p className="text-sm text-gray-500">Latest communication analysis results</p>
        </div>
        
        <div className="text-center py-8">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
          <h4 className="mt-2 text-sm font-medium text-gray-900">No analyses yet</h4>
          <p className="mt-1 text-sm text-gray-500">
            Run your first analysis to see results here.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Recent Analyses</h3>
        <p className="text-sm text-gray-500">Latest communication analysis results</p>
      </div>

      <div className="space-y-4">
        {sortedAnalyses.map((analysis, index) => (
          <div 
            key={`${analysis.date}-${analysis.channel_name}-${index}`}
            className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                {getAnalysisIcon(analysis.dignity_score, analysis.psychological_safety_score, analysis.burnout_risk_level)}
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    #{analysis.channel_name}
                  </h4>
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    <Clock className="h-3 w-3" />
                    <span>{formatDistanceToNow(new Date(analysis.date), { addSuffix: true })}</span>
                    <span>•</span>
                    <span>{analysis.message_count} messages</span>
                  </div>
                </div>
              </div>
              
              <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getRiskColor(analysis.burnout_risk_level)}`}>
                {analysis.burnout_risk_level} risk
              </div>
            </div>

            {/* Metrics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className={`text-lg font-semibold ${getDignityScoreColor(analysis.dignity_score)}`}>
                  {analysis.dignity_score > 0 ? '+' : ''}{analysis.dignity_score.toFixed(1)}
                </div>
                <div className="text-xs text-gray-500">Dignity Score</div>
              </div>
              <div className="text-center p-2 bg-gray-50 rounded">
                <div className={`text-lg font-semibold ${getSafetyScoreColor(analysis.psychological_safety_score)}`}>
                  {Math.round(analysis.psychological_safety_score)}%
                </div>
                <div className="text-xs text-gray-500">Safety Score</div>
              </div>
            </div>

            {/* Quick Assessment */}
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="text-xs text-gray-600">
                {analysis.dignity_score >= 7 && analysis.psychological_safety_score >= 80 ? (
                  <span className="text-green-600">✅ Healthy communication patterns detected</span>
                ) : analysis.burnout_risk_level === 'high' || analysis.dignity_score < 3 ? (
                  <span className="text-red-600">⚠️ Communication issues identified - review recommended</span>
                ) : (
                  <span className="text-blue-600">ℹ️ Moderate communication health - monitor trends</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary Footer */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-gray-900">{analyses.length}</div>
            <div className="text-xs text-gray-500">Total Analyses</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {(analyses.reduce((sum, a) => sum + a.dignity_score, 0) / analyses.length).toFixed(1)}
            </div>
            <div className="text-xs text-gray-500">Avg Dignity</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {Math.round(analyses.reduce((sum, a) => sum + a.psychological_safety_score, 0) / analyses.length)}%
            </div>
            <div className="text-xs text-gray-500">Avg Safety</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RecentAnalyses