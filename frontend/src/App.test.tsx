import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from './App';

// Mock fetch responses
const mockSuccessResponse = {
  summary: "The communication is positive and collaborative.",
  tone: "harmony" as const,
  warnings: [],
  recommendations: ["Continue the positive communication style."],
  basic_analysis: {
    tension_score: 0.5,
    harmony_score: 4.2,
    message_count: 2
  }
};

const mockTensionResponse = {
  summary: "The communication shows significant tension and conflict.",
  tone: "tension" as const,
  warnings: ["Aggressive language detected", "Dismissive communication patterns"],
  recommendations: [
    "Practice active listening",
    "Use 'I' statements instead of 'you' statements"
  ],
  basic_analysis: {
    tension_score: 5.0,
    harmony_score: 0.5,
    message_count: 3
  }
};

const mockErrorResponse = {
  error: "Expected an array of messages"
};

// Helper function to set textarea value directly (avoids userEvent typing issues with special characters)
const setTextareaValue = (textarea: HTMLElement, value: string) => {
  fireEvent.change(textarea, { target: { value } });
};

describe('App Component', () => {
  beforeEach(() => {
    // Reset fetch mock before each test
    (fetch as jest.Mock).mockClear();
  });

  describe('Initial Render', () => {
    test('renders main heading and description', () => {
      render(<App />);
      
      expect(screen.getByRole('heading', { name: /vibe check/i })).toBeInTheDocument();
      expect(screen.getByText(/ai-powered communication analysis/i)).toBeInTheDocument();
    });

    test('renders textarea with placeholder', () => {
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      expect(textarea).toBeInTheDocument();
      expect(textarea).toHaveAttribute('placeholder');
      expect(textarea.getAttribute('placeholder')).toContain('JSON array');
    });

    test('renders analyze button', () => {
      render(<App />);
      
      const button = screen.getByRole('button', { name: /analyze messages/i });
      expect(button).toBeInTheDocument();
    });

    test('analyze button is disabled when textarea is empty', () => {
      render(<App />);
      
      const button = screen.getByRole('button', { name: /analyze messages/i });
      expect(button).toBeDisabled();
    });

    test('no results or error messages initially displayed', () => {
      render(<App />);
      
      expect(screen.queryByText(/analysis results/i)).not.toBeInTheDocument();
      expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
    });
  });

  describe('Input Handling', () => {
    test('enables analyze button when text is entered', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      expect(button).toBeDisabled();
      
      await user.type(textarea, 'Hello world');
      
      expect(button).toBeEnabled();
    });

    test('handles JSON array input correctly', async () => {
      render(<App />);

      const textarea = screen.getByRole('textbox');
      const jsonInput = '["Hello team!", "Great work everyone!"]';

      setTextareaValue(textarea, jsonInput);

      expect(textarea).toHaveValue(jsonInput);
    });

    test('handles line-separated input correctly', async () => {
      const user = userEvent.setup();
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const lineInput = 'Hello team!\nGreat work everyone!';
      
      await user.type(textarea, lineInput);
      
      expect(textarea).toHaveValue(lineInput);
    });
  });

  describe('Successful Analysis Flow', () => {
    test('displays loading state during analysis', async () => {
      const user = userEvent.setup();
      
      // Mock a delayed response
      (fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve(mockSuccessResponse)
          }), 100)
        )
      );
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["Hello team!"]');
      await user.click(button);
      
      // Check loading state
      expect(screen.getByText(/analyzing/i)).toBeInTheDocument();
      expect(button).toBeDisabled();
      
      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByText(/analyzing/i)).not.toBeInTheDocument();
      });
    });

    test('displays harmony results correctly', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuccessResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["Hello team!", "Great work!"]');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
      });
      
      // Check tone badge
      expect(screen.getByText(/overall tone: harmony/i)).toBeInTheDocument();
      
      // Check summary
      expect(screen.getByText(mockSuccessResponse.summary)).toBeInTheDocument();
      
      // Check recommendations
      expect(screen.getByText(/recommendations/i)).toBeInTheDocument();
      expect(screen.getByText(mockSuccessResponse.recommendations[0])).toBeInTheDocument();
      
      // Check metrics
      expect(screen.getByText('2')).toBeInTheDocument(); // message count
      expect(screen.getByText('0.5')).toBeInTheDocument(); // tension score
      expect(screen.getByText('4.2')).toBeInTheDocument(); // harmony score
    });

    test('displays tension results with warnings', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockTensionResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["This is wrong!", "You never listen!", "I hate this!"]');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
      });
      
      // Check tone badge (should be red for tension)
      expect(screen.getByText(/overall tone: tension/i)).toBeInTheDocument();
      
      // Check warnings section
      expect(screen.getByText(/potential issues/i)).toBeInTheDocument();
      expect(screen.getByText(mockTensionResponse.warnings[0])).toBeInTheDocument();
      expect(screen.getByText(mockTensionResponse.warnings[1])).toBeInTheDocument();
      
      // Check recommendations
      expect(screen.getByText(mockTensionResponse.recommendations[0])).toBeInTheDocument();
      expect(screen.getByText(mockTensionResponse.recommendations[1])).toBeInTheDocument();
    });

    test('calls correct API endpoint with proper data', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuccessResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      const testMessages = '["Hello team!", "Great work!"]';
      setTextareaValue(textarea, testMessages);
      await user.click(button);
      
      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5008/api/v1/analyze',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(["Hello team!", "Great work!"]),
          }
        );
      });
    });
  });

  describe('Error Handling', () => {
    test('displays error message on API failure', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve(mockErrorResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, 'invalid input');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument();
      });
      
      expect(screen.getByText(mockErrorResponse.error)).toBeInTheDocument();
      expect(screen.queryByText(/analysis results/i)).not.toBeInTheDocument();
    });

    test('displays error for network failure', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["Test message"]');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });
    });

    test('disables button for empty input', async () => {
      const user = userEvent.setup();
      render(<App />);

      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });

      // Initially disabled
      expect(button).toBeDisabled();

      // Add some text to enable
      await user.type(textarea, 'test');
      expect(button).toBeEnabled();

      // Clear to just whitespace - should disable again
      await user.clear(textarea);
      await user.type(textarea, '   '); // just whitespace
      expect(button).toBeDisabled();
    });
  });

  describe('Input Format Handling', () => {
    test('parses JSON array input correctly', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuccessResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["Message 1", "Message 2"]');
      await user.click(button);
      
      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            body: JSON.stringify(["Message 1", "Message 2"])
          })
        );
      });
    });

    test('converts line-separated input to array', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuccessResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      await user.type(textarea, 'Message 1\nMessage 2\nMessage 3');
      await user.click(button);
      
      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            body: JSON.stringify(["Message 1", "Message 2", "Message 3"])
          })
        );
      });
    });

    test('handles invalid JSON gracefully', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: "No valid messages found" })
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '{invalid json}');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/no valid messages found/i)).toBeInTheDocument();
      });
    });
  });

  describe('UI State Management', () => {
    test('clears previous results when new analysis starts', async () => {
      const user = userEvent.setup();
      
      // First successful analysis
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuccessResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["First test"]');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
      });
      
      // Clear textarea and add new content
      await user.clear(textarea);
      setTextareaValue(textarea, '["Second test"]');
      
      // Mock second analysis with delay
      (fetch as jest.Mock).mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve(mockTensionResponse)
          }), 100)
        )
      );
      
      await user.click(button);
      
      // Previous results should be cleared during loading
      expect(screen.queryByText(mockSuccessResponse.summary)).not.toBeInTheDocument();
    });

    test('button returns to normal state after analysis', async () => {
      const user = userEvent.setup();
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockSuccessResponse)
      });
      
      render(<App />);
      
      const textarea = screen.getByRole('textbox');
      const button = screen.getByRole('button', { name: /analyze messages/i });
      
      setTextareaValue(textarea, '["Test"]');
      await user.click(button);
      
      await waitFor(() => {
        expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
      });
      
      // Button should be enabled again and show normal text
      expect(button).toBeEnabled();
      expect(button).toHaveTextContent(/analyze messages/i);
    });
  });
});
