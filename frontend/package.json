{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"date-fns": "^4.1.0", "lucide-react": "^0.316.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2", "recharts": "^2.10.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/node": "^20.10.6", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.11"}}