# Vibe Check Frontend

Modern React TypeScript frontend for the Vibe Check communication analysis platform.

## Features

### Core Interface
- **Single Message Analysis**: Manual input for individual message analysis
- **Slack Integration**: Real-time analysis of Slack channels
- **Comprehensive Results**: Dignity scores, psychological safety, and actionable insights

### Team Dashboard
- **Multi-Team Management**: Switch between different teams and workspaces
- **Interactive Charts**: Historical trends with Recharts visualization
- **Channel Comparison**: Side-by-side health metrics across channels
- **Team Member Insights**: Individual participation and communication analysis
- **Real-time Metrics**: Live team health indicators and trend analysis

### User Experience
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Real-time Updates**: Loading states and progress indicators
- **Error Handling**: Comprehensive error states with user-friendly messages
- **Accessibility**: ARIA labels and keyboard navigation support

## Architecture

### Component Structure
```
src/
├── components/
│   ├── Dashboard.tsx           # Main dashboard layout
│   ├── TeamSelector.tsx        # Team selection dropdown
│   ├── MetricsCard.tsx         # Reusable metrics display
│   ├── TrendChart.tsx          # Historical trend visualization
│   ├── ChannelComparison.tsx   # Channel health comparison
│   ├── TeamMemberInsights.tsx  # Individual member analysis
│   └── RecentAnalyses.tsx      # Recent analysis list
├── App.tsx                     # Main application component
├── main.tsx                    # Application entry point
└── index.css                   # Global styles and Tailwind imports
```

### State Management
- **React Hooks**: useState, useEffect for local component state
- **TypeScript**: Full type safety across all components
- **Mock Data**: Realistic demo data with easy API integration points

### Design System
- **Tailwind CSS**: Utility-first styling with consistent design tokens
- **Lucide React**: Beautiful, customizable icons
- **Responsive Grid**: Mobile-first responsive layouts
- **Color Palette**: Semantic color system for health indicators

## Development

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Available Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run lint` - Run ESLint for code quality
- `npm run preview` - Preview production build locally
- `npm test` - Run Jest test suite
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Generate test coverage report

### Development Guidelines

#### Component Development
1. **TypeScript First**: All components fully typed
2. **Props Interfaces**: Clear interface definitions for all props
3. **Error Boundaries**: Graceful error handling in components
4. **Loading States**: User feedback during async operations

#### Styling Guidelines
1. **Tailwind Classes**: Use utility classes for styling
2. **Responsive Design**: Mobile-first approach with responsive modifiers
3. **Semantic Colors**: Use semantic color classes (green for success, red for errors)
4. **Consistent Spacing**: Use Tailwind spacing scale consistently

#### State Management
1. **Local State**: Use useState for component-specific state
2. **Prop Drilling**: Keep prop chains short and manageable
3. **API Integration**: Centralized fetch logic with error handling
4. **Loading States**: Consistent loading and error state patterns

## Testing

### Test Strategy
- **Component Tests**: React Testing Library for UI testing
- **Unit Tests**: Jest for utility function testing
- **Integration Tests**: API interaction testing with MSW
- **Accessibility Tests**: ARIA and keyboard navigation testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Test Structure
```
src/
├── components/
│   ├── Dashboard.test.tsx
│   ├── MetricsCard.test.tsx
│   └── ...
├── App.test.tsx
└── setupTests.ts
```

## Configuration

### Environment Variables
The frontend reads the backend API URL from the environment. For development, it defaults to `http://localhost:5008`.

### Build Configuration
- **Vite**: Fast build tool with TypeScript support
- **ESLint**: Code quality and consistency
- **PostCSS**: CSS processing with Tailwind
- **TypeScript**: Strict type checking enabled

### Browser Support
- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Progressive enhancement for older browsers

## Performance

### Optimization Strategies
- **Component Lazy Loading**: Dynamic imports for large components
- **Memoization**: React.memo for expensive re-renders
- **Bundle Splitting**: Automatic code splitting with Vite
- **Image Optimization**: Responsive images and lazy loading

### Best Practices
- **Minimal Re-renders**: Optimized state updates
- **Efficient Data Structures**: Normalized data for complex state
- **Debounced Inputs**: Reduced API calls for user inputs
- **Error Boundaries**: Graceful degradation on component errors

## Deployment

### Production Build
```bash
npm run build
```

### Build Output
- Optimized bundle in `dist/` directory
- Static assets with cache headers
- Minified CSS and JavaScript
- Source maps for debugging

### Deployment Targets
- **Static Hosting**: Netlify, Vercel, GitHub Pages
- **CDN Distribution**: CloudFront, CloudFlare
- **Container Deployment**: Docker with nginx

## Contributing

### Code Style
- **Prettier**: Automatic code formatting
- **ESLint**: Code quality enforcement
- **TypeScript**: Strict type checking
- **Conventional Commits**: Standardized commit messages

### Pull Request Process
1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request with clear description

## Dependencies

### Core Dependencies
- **React 19**: Latest React with modern features
- **TypeScript**: Type-safe JavaScript development
- **Recharts**: Professional data visualization
- **Lucide React**: Beautiful icon library
- **Date-fns**: Modern date utility library

### Development Dependencies
- **Vite**: Fast build tool and dev server
- **Tailwind CSS**: Utility-first CSS framework
- **Jest**: JavaScript testing framework
- **React Testing Library**: Component testing utilities
- **ESLint**: Code linting and quality assurance