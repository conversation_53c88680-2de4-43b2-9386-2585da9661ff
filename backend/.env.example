# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Slack Integration Configuration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token-here

# Debug Configuration
# Set to "true" to enable detailed logging of OpenAI API requests and responses
DEBUG_PROMPT=false

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=true

# Security Configuration
ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# Rate Limiting Configuration
RATE_LIMIT_DEFAULT=100 per hour
RATE_LIMIT_ANALYZE=10 per minute
RATE_LIMIT_SLACK_ANALYZE=5 per minute

# Instructions for Slack Integration:
# 1. Go to https://api.slack.com/apps
# 2. Create a new app or select existing app
# 3. Go to "OAuth & Permissions"
# 4. Add bot scopes: channels:read, channels:history, groups:read, groups:history
# 5. Install app to workspace
# 6. Copy the "Bot User OAuth Token" and paste above
