"""
Slack Connector - Integration module for Slack workspace communication analysis.

This module provides functionality to connect to Slack workspaces, retrieve channel messages,
and integrate with the Vibe Check analysis engine for real-time team communication insights.
"""

import os
import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional, Tuple
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class SlackConnector:
    """
    Slack workspace connector for retrieving and analyzing team communication.
    
    Provides methods to authenticate with Slack, retrieve channel messages,
    and prepare them for analysis with the Vibe Check engine.
    """
    
    def __init__(self, token: Optional[str] = None):
        """
        Initialize the Slack connector.
        
        Args:
            token: Slack Bot Token. If not provided, will read from SLACK_BOT_TOKEN environment variable.
        """
        self.token = token or os.getenv('SLACK_BOT_TOKEN')
        if not self.token:
            raise ValueError("Slack <PERSON><PERSON> not found. Please set SLACK_BOT_TOKEN environment variable or pass token parameter.")
        
        # Initialize the Slack client
        self.client = WebClient(token=self.token)
        
        # Test connection on initialization
        self._test_connection()
    
    def _test_connection(self) -> None:
        """Test the Slack API connection and log workspace info."""
        try:
            response = self.client.auth_test()
            logger.info(f"Connected to Slack workspace: {response['team']} as {response['user']}")
        except SlackApiError as e:
            logger.error(f"Failed to connect to Slack: {e.response['error']}")
            raise ConnectionError(f"Slack API connection failed: {e.response['error']}")
    
    def get_channels(self, types: str = "public_channel,private_channel") -> List[Dict[str, Any]]:
        """
        Retrieve list of channels the bot has access to.
        
        Args:
            types: Comma-separated list of channel types to include
            
        Returns:
            List of channel dictionaries with id, name, and metadata
        """
        try:
            response = self.client.conversations_list(
                types=types,
                exclude_archived=True,
                limit=1000
            )
            
            channels = []
            for channel in response['channels']:
                channels.append({
                    'id': channel['id'],
                    'name': channel['name'],
                    'is_private': channel.get('is_private', False),
                    'member_count': channel.get('num_members', 0),
                    'purpose': channel.get('purpose', {}).get('value', ''),
                    'topic': channel.get('topic', {}).get('value', '')
                })
            
            logger.info(f"Retrieved {len(channels)} channels")
            return channels
            
        except SlackApiError as e:
            logger.error(f"Failed to retrieve channels: {e.response['error']}")
            raise RuntimeError(f"Failed to retrieve channels: {e.response['error']}")
    
    def get_channel_messages(self, 
                           channel_id: str, 
                           days_back: int = 7,
                           limit: int = 100) -> Tuple[List[str], Dict[str, Any]]:
        """
        Retrieve recent messages from a specific channel.
        
        Args:
            channel_id: Slack channel ID
            days_back: Number of days to look back for messages
            limit: Maximum number of messages to retrieve
            
        Returns:
            Tuple of (messages_list, metadata_dict)
        """
        try:
            # Calculate time range
            oldest_time = datetime.now() - timedelta(days=days_back)
            oldest_timestamp = oldest_time.timestamp()
            
            # Get channel info
            channel_info = self.client.conversations_info(channel=channel_id)
            channel_name = channel_info['channel']['name']
            
            # Retrieve messages
            response = self.client.conversations_history(
                channel=channel_id,
                oldest=str(oldest_timestamp),
                limit=limit,
                inclusive=True
            )
            
            messages = []
            metadata = {
                'channel_id': channel_id,
                'channel_name': channel_name,
                'date_range': f"{days_back} days",
                'total_messages': len(response['messages']),
                'filtered_messages': 0,
                'users': set()
            }
            
            for message in response['messages']:
                # Skip bot messages and system messages
                if message.get('subtype') in ['bot_message', 'channel_join', 'channel_leave']:
                    continue
                
                # Skip messages without text
                if 'text' not in message or not message['text'].strip():
                    continue
                
                # Clean and process message text
                clean_text = self._clean_message_text(message['text'])
                if clean_text:
                    messages.append(clean_text)
                    metadata['users'].add(message.get('user', 'unknown'))
            
            metadata['filtered_messages'] = len(messages)
            metadata['unique_users'] = len(metadata['users'])
            metadata['users'] = list(metadata['users'])  # Convert set to list for JSON serialization
            
            logger.info(f"Retrieved {len(messages)} messages from #{channel_name} ({days_back} days)")
            return messages, metadata
            
        except SlackApiError as e:
            logger.error(f"Failed to retrieve messages from channel {channel_id}: {e.response['error']}")
            raise RuntimeError(f"Failed to retrieve channel messages: {e.response['error']}")
    
    def _clean_message_text(self, text: str) -> str:
        """
        Clean and normalize Slack message text for analysis.
        
        Args:
            text: Raw Slack message text
            
        Returns:
            Cleaned text ready for analysis
        """
        import re
        
        # Remove Slack-specific formatting
        # Remove user mentions like <@U123456>
        text = re.sub(r'<@[A-Z0-9]+>', '', text)
        
        # Remove channel mentions like <#C123456|general>
        text = re.sub(r'<#[A-Z0-9]+\|[^>]+>', '', text)
        
        # Remove URLs like <https://example.com|Example>
        text = re.sub(r'<https?://[^>]+>', '', text)
        
        # Remove special Slack formatting
        text = re.sub(r'<[^>]+>', '', text)
        
        # Clean up whitespace
        text = ' '.join(text.split())
        
        # Remove empty messages or messages that are just punctuation
        if not text or len(text.strip()) < 3:
            return ""
        
        return text.strip()
    
    def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """
        Get information about a specific user.
        
        Args:
            user_id: Slack user ID
            
        Returns:
            Dictionary with user information
        """
        try:
            response = self.client.users_info(user=user_id)
            user = response['user']
            
            return {
                'id': user['id'],
                'name': user.get('name', 'Unknown'),
                'real_name': user.get('real_name', ''),
                'display_name': user.get('profile', {}).get('display_name', ''),
                'title': user.get('profile', {}).get('title', ''),
                'is_bot': user.get('is_bot', False)
            }
            
        except SlackApiError as e:
            logger.error(f"Failed to get user info for {user_id}: {e.response['error']}")
            return {
                'id': user_id,
                'name': 'Unknown User',
                'real_name': '',
                'display_name': '',
                'title': '',
                'is_bot': False
            }


def test_slack_connection() -> bool:
    """
    Test function to verify Slack connection and basic functionality.
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        connector = SlackConnector()
        channels = connector.get_channels()
        logger.info(f"Slack connection test successful. Found {len(channels)} channels.")
        return True
    except Exception as e:
        logger.error(f"Slack connection test failed: {e}")
        return False


# Example usage and testing
if __name__ == "__main__":
    # Configure logging for testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Test the connection
    if test_slack_connection():
        try:
            connector = SlackConnector()
            
            # List available channels
            channels = connector.get_channels()
            print(f"\nAvailable channels ({len(channels)}):")
            for channel in channels[:5]:  # Show first 5 channels
                print(f"  #{channel['name']} ({channel['id']}) - {channel['member_count']} members")
            
            # If channels exist, test message retrieval from first channel
            if channels:
                first_channel = channels[0]
                print(f"\nTesting message retrieval from #{first_channel['name']}...")
                
                messages, metadata = connector.get_channel_messages(
                    first_channel['id'], 
                    days_back=3, 
                    limit=10
                )
                
                print(f"Retrieved {len(messages)} messages")
                print(f"Metadata: {metadata}")
                
                if messages:
                    print("\nSample messages:")
                    for i, msg in enumerate(messages[:3]):
                        print(f"  {i+1}. {msg[:100]}{'...' if len(msg) > 100 else ''}")
                        
        except Exception as e:
            print(f"Error during testing: {e}")
    else:
        print("Slack connection test failed. Please check your configuration.")