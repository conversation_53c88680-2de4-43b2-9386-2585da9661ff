# cspell:ignore jsonify levelname
import logging
import os
import re
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from typing import List, Dict, Any
from dotenv import load_dotenv
from vibe_check_engine import analyze_messages as ai_analyze_messages
from slack_connector import SlackConnector
from validation import validate_json, MessageAnalysisSchema, SlackAnalysisSchema
from secure_logging import log_request_safely, log_analysis_result_safely, setup_secure_logging

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set up secure logging to prevent data leakage
setup_secure_logging()

# Debug flag for OpenAI API interactions
DEBUG_PROMPT = os.getenv("DEBUG_PROMPT", "false").lower() == "true"
if DEBUG_PROMPT:
    logger.info("Debug prompt logging enabled")

app = Flask(__name__)

# Configure CORS with specific allowed origins
allowed_origins = os.getenv('ALLOWED_ORIGINS', 'http://localhost:5173').split(',')
CORS(app, origins=[origin.strip() for origin in allowed_origins])

# Configure rate limiting
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=[os.getenv('RATE_LIMIT_DEFAULT', '100 per hour')]
)

class ToneAnalyzer:
    """Analyzes communication tone to detect tension or harmony."""
    
    def __init__(self):
        # Words/phrases that indicate tension or conflict
        self.tension_indicators = {
            'high': ['angry', 'furious', 'hate', 'terrible', 'awful', 'disgusting', 'stupid', 'idiot', 'wrong', 'never'],
            'medium': ['disagree', 'no', 'but', 'however', 'unfortunately', 'problem', 'issue', 'concern', 'worried'],
            'low': ['maybe', 'perhaps', 'might', 'could be', 'not sure', 'question']
        }
        
        # Words/phrases that indicate harmony or positive communication
        self.harmony_indicators = {
            'high': ['love', 'excellent', 'amazing', 'perfect', 'wonderful', 'fantastic', 'agree', 'yes', 'absolutely'],
            'medium': ['good', 'nice', 'thanks', 'appreciate', 'understand', 'makes sense', 'helpful', 'support'],
            'low': ['okay', 'fine', 'sure', 'alright', 'sounds good']
        }
    
    def analyze_messages(self, messages: List[str]) -> Dict[str, Any]:
        """Analyze a list of messages and return tone summary."""
        if not messages:
            return {
                'summary': 'No messages to analyze.',
                'tone': 'neutral',
                'tension_score': 0,
                'harmony_score': 0,
                'details': []
            }
        
        total_tension = 0
        total_harmony = 0
        message_details = []
        
        for i, message in enumerate(messages):
            if not isinstance(message, str):
                continue
                
            message_lower = message.lower()
            
            # Calculate tension score for this message
            tension_score = self._calculate_score(message_lower, self.tension_indicators)
            
            # Calculate harmony score for this message
            harmony_score = self._calculate_score(message_lower, self.harmony_indicators)
            
            total_tension += tension_score
            total_harmony += harmony_score
            
            message_details.append({
                'message_index': i + 1,
                'tension_score': tension_score,
                'harmony_score': harmony_score,
                'preview': message[:50] + '...' if len(message) > 50 else message
            })
        
        # Normalize scores based on number of messages
        avg_tension = total_tension / len(messages) if messages else 0
        avg_harmony = total_harmony / len(messages) if messages else 0
        
        # Determine overall tone
        tone, summary = self._generate_summary(avg_tension, avg_harmony, len(messages))
        
        return {
            'summary': summary,
            'tone': tone,
            'tension_score': round(avg_tension, 2),
            'harmony_score': round(avg_harmony, 2),
            'message_count': len(messages),
            'details': message_details
        }
    
    def _calculate_score(self, text: str, indicators: Dict[str, List[str]]) -> float:
        """Calculate score based on presence of indicator words."""
        score = 0
        
        for level, words in indicators.items():
            for word in words:
                # Count occurrences of each indicator word
                count = len(re.findall(r'\b' + re.escape(word) + r'\b', text))
                if level == 'high':
                    score += count * 3
                elif level == 'medium':
                    score += count * 2
                else:  # low
                    score += count * 1
        
        return score
    
    def _generate_summary(self, tension: float, harmony: float, message_count: int) -> tuple:
        """Generate plain English summary of communication tone."""
        
        if tension > harmony * 1.5:
            if tension > 5:
                tone = 'highly_tense'
                summary = f"The communication shows significant tension across {message_count} messages. " \
                         f"There are clear signs of conflict, disagreement, or negative sentiment. " \
                         f"The conversation may benefit from a more collaborative approach."
            elif tension > 2:
                tone = 'moderately_tense'
                summary = f"The communication shows moderate tension in {message_count} messages. " \
                         f"There are some signs of disagreement or concern that should be addressed."
            else:
                tone = 'slightly_tense'
                summary = f"The communication shows mild tension across {message_count} messages. " \
                         f"There are minor signs of disagreement but nothing major."
        
        elif harmony > tension * 1.5:
            if harmony > 5:
                tone = 'highly_harmonious'
                summary = f"The communication is very positive and harmonious across {message_count} messages. " \
                         f"There are strong signs of agreement, collaboration, and positive sentiment."
            elif harmony > 2:
                tone = 'moderately_harmonious'
                summary = f"The communication shows good harmony in {message_count} messages. " \
                         f"There are positive signs of collaboration and agreement."
            else:
                tone = 'slightly_harmonious'
                summary = f"The communication is generally positive across {message_count} messages " \
                         f"with some signs of agreement and collaboration."
        
        else:
            tone = 'neutral'
            summary = f"The communication appears neutral across {message_count} messages. " \
                     f"There's a balanced mix of different sentiments without strong indicators " \
                     f"of either significant tension or harmony."
        
        return tone, summary

# Initialize tone analyzer
analyzer = ToneAnalyzer()

@app.route('/api/v1/analyze', methods=['POST'])
@limiter.limit(os.getenv('RATE_LIMIT_ANALYZE', '10 per minute'))
@validate_json(MessageAnalysisSchema)
def analyze_communication():
    """Analyze communication tone from an array of messages."""
    try:
        # Get validated data (validation handled by decorator)
        data = request.validated_data
        
        # Log the analysis request safely
        log_request_safely(logger, '/api/v1/analyze', len(data))

        # Perform AI-powered tone analysis
        ai_result = ai_analyze_messages(data)

        # Also get basic analysis for comparison
        basic_result = analyzer.analyze_messages(data)

        # Combine results with AI analysis as primary
        result = {
            'summary': ai_result.get('summary', 'Analysis completed'),
            'tone': ai_result.get('tone', 'neutral'),
            'dignity_score': ai_result.get('dignity_score', 0),
            'psychological_safety_score': ai_result.get('psychological_safety_score', 50),
            'burnout_risk_level': ai_result.get('burnout_risk_level', 'medium'),
            'warnings': ai_result.get('warnings', []),
            'recommendations': ai_result.get('recommendations', []),
            'patterns': ai_result.get('patterns', {
                'dignity_violations': [],
                'communication_gaps': [],
                'positive_patterns': []
            }),
            'basic_analysis': {
                'tension_score': basic_result.get('tension_score', 0),
                'harmony_score': basic_result.get('harmony_score', 0),
                'message_count': basic_result.get('message_count', len(data))
            }
        }

        # Log analysis results safely
        log_analysis_result_safely(logger, result)

        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error during analysis: {str(e)}")
        return jsonify({
            'error': 'Internal server error occurred during analysis'
        }), 500

@app.route('/api/v1/slack/channels', methods=['GET'])
def get_slack_channels():
    """Get list of available Slack channels."""
    try:
        connector = SlackConnector()
        channels = connector.get_channels()
        
        return jsonify({
            'channels': channels,
            'count': len(channels)
        }), 200
        
    except ValueError as e:
        logger.error(f"Slack configuration error: {e}")
        return jsonify({
            'error': 'Slack not configured. Please set SLACK_BOT_TOKEN environment variable.'
        }), 400
        
    except Exception as e:
        logger.error(f"Error retrieving Slack channels: {e}")
        return jsonify({
            'error': 'Failed to retrieve Slack channels'
        }), 500

@app.route('/api/v1/slack/analyze-channel', methods=['POST'])
@limiter.limit(os.getenv('RATE_LIMIT_SLACK_ANALYZE', '5 per minute'))
@validate_json(SlackAnalysisSchema)
def analyze_slack_channel():
    """Analyze messages from a specific Slack channel."""
    try:
        # Get validated data (validation handled by decorator)
        data = request.validated_data
        channel_id = data['channel_id']
        days_back = data['days_back']
        limit = data['limit']
        
        # Log request safely
        log_request_safely(logger, '/api/v1/slack/analyze-channel', 0, 
                          channel_id=f"C{channel_id[-6:]}", days_back=days_back, limit=limit)
        
        # Connect to Slack and retrieve messages
        connector = SlackConnector()
        messages, metadata = connector.get_channel_messages(
            channel_id=channel_id,
            days_back=days_back,
            limit=limit
        )
        
        if not messages:
            return jsonify({
                'error': 'No messages found in the specified channel and time range',
                'metadata': metadata
            }), 404
        
        # Perform AI analysis on the messages
        ai_result = ai_analyze_messages(messages)
        
        # Also get basic analysis for comparison
        basic_result = analyzer.analyze_messages(messages)
        
        # Combine results with metadata
        result = {
            'summary': ai_result.get('summary', 'Analysis completed'),
            'tone': ai_result.get('tone', 'neutral'),
            'dignity_score': ai_result.get('dignity_score', 0),
            'psychological_safety_score': ai_result.get('psychological_safety_score', 50),
            'burnout_risk_level': ai_result.get('burnout_risk_level', 'medium'),
            'warnings': ai_result.get('warnings', []),
            'recommendations': ai_result.get('recommendations', []),
            'patterns': ai_result.get('patterns', {
                'dignity_violations': [],
                'communication_gaps': [],
                'positive_patterns': []
            }),
            'slack_metadata': metadata,
            'basic_analysis': {
                'tension_score': basic_result.get('tension_score', 0),
                'harmony_score': basic_result.get('harmony_score', 0),
                'message_count': basic_result.get('message_count', len(messages))
            }
        }
        
        # Log analysis results safely
        log_analysis_result_safely(logger, result)
        
        return jsonify(result), 200
        
    except ValueError as e:
        logger.error(f"Slack configuration error: {e}")
        return jsonify({
            'error': 'Slack not configured. Please set SLACK_BOT_TOKEN environment variable.'
        }), 400
        
    except RuntimeError as e:
        logger.error(f"Slack API error: {e}")
        return jsonify({
            'error': str(e)
        }), 400
        
    except Exception as e:
        logger.error(f"Error during Slack channel analysis: {e}")
        return jsonify({
            'error': 'Internal server error occurred during Slack analysis'
        }), 500

@app.route('/api/v1/slack/test-connection', methods=['GET'])
def test_slack_connection():
    """Test Slack API connection."""
    try:
        connector = SlackConnector()
        channels = connector.get_channels()
        
        return jsonify({
            'status': 'connected',
            'channel_count': len(channels),
            'message': 'Slack connection successful'
        }), 200
        
    except ValueError as e:
        return jsonify({
            'status': 'configuration_error',
            'message': 'Slack not configured. Please set SLACK_BOT_TOKEN environment variable.'
        }), 400
        
    except Exception as e:
        logger.error(f"Slack connection test failed: {e}")
        return jsonify({
            'status': 'connection_failed',
            'message': 'Failed to connect to Slack API'
        }), 500

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """Simple health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'tone-analyzer'}), 200

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(405)
def method_not_allowed(error):
    """Handle 405 errors."""
    return jsonify({'error': 'Method not allowed'}), 405

if __name__ == '__main__':
    logger.info("Starting Flask application...")
    app.run(debug=True, host='0.0.0.0', port=5008)
