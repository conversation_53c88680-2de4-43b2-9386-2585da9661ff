"""
Secure logging utilities to prevent sensitive data exposure.
"""

import logging
import re
from typing import Any, Dict, List

# Sensitive patterns to redact
SENSITIVE_PATTERNS = [
    (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', '[EMAIL_REDACTED]'),  # Email addresses
    (r'xoxb-[0-9]+-[0-9]+-[0-9a-zA-Z]+', '[SLACK_TOKEN_REDACTED]'),  # Slack bot tokens
    (r'sk-[a-zA-Z0-9]{10,}', '[API_KEY_REDACTED]'),  # API keys starting with sk-
    (r'\b[A-Za-z0-9]{20,}\b', '[TOKEN_REDACTED]'),  # Generic long tokens
]

def sanitize_for_logging(data: Any, max_length: int = 100) -> str:
    """
    Sanitize data for safe logging by removing sensitive information.
    
    Args:
        data: Data to sanitize (string, dict, list, etc.)
        max_length: Maximum length for string representation
        
    Returns:
        Sanitized string safe for logging
    """
    # Convert to string representation
    if isinstance(data, (dict, list)):
        # For collections, show structure but not content
        if isinstance(data, dict):
            keys = list(data.keys())
            return f"dict with {len(data)} keys: {keys[:3]}{'...' if len(keys) > 3 else ''}"
        elif isinstance(data, list):
            return f"list with {len(data)} items"
    
    # Convert to string
    text = str(data)
    
    # Apply redaction patterns
    for pattern, replacement in SENSITIVE_PATTERNS:
        text = re.sub(pattern, replacement, text)
    
    # Truncate if too long
    if len(text) > max_length:
        text = text[:max_length] + '...[TRUNCATED]'
    
    return text

def log_request_safely(logger: logging.Logger, endpoint: str, data_size: int, **kwargs):
    """
    Log request information safely without exposing sensitive data.
    
    Args:
        logger: Logger instance
        endpoint: API endpoint name
        data_size: Size of request data
        **kwargs: Additional safe metadata to log
    """
    safe_metadata = {k: sanitize_for_logging(v, max_length=50) for k, v in kwargs.items()}
    logger.info(f"API request to {endpoint} - data_size: {data_size}, metadata: {safe_metadata}")

def log_analysis_result_safely(logger: logging.Logger, result: Dict[str, Any]):
    """
    Log analysis results safely, focusing on metrics rather than content.
    
    Args:
        logger: Logger instance
        result: Analysis result dictionary
    """
    safe_metrics = {
        'tone': result.get('tone', 'unknown'),
        'dignity_score': result.get('dignity_score', 'N/A'),
        'psychological_safety_score': result.get('psychological_safety_score', 'N/A'),
        'burnout_risk_level': result.get('burnout_risk_level', 'unknown'),
        'warning_count': len(result.get('warnings', [])),
        'recommendation_count': len(result.get('recommendations', []))
    }
    logger.info(f"Analysis complete - metrics: {safe_metrics}")

class SecureFormatter(logging.Formatter):
    """
    Custom logging formatter that automatically sanitizes sensitive data.
    """
    
    def format(self, record):
        # Sanitize the message
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = sanitize_for_logging(record.msg, max_length=500)
        
        # Sanitize args if present
        if hasattr(record, 'args') and record.args:
            record.args = tuple(sanitize_for_logging(arg, max_length=200) for arg in record.args)
        
        return super().format(record)

def setup_secure_logging():
    """
    Set up secure logging configuration that prevents data leakage.
    """
    # Create secure formatter
    secure_formatter = SecureFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Apply to all handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers:
        handler.setFormatter(secure_formatter)
    
    # Set appropriate log levels
    logging.getLogger('werkzeug').setLevel(logging.WARNING)  # Reduce Flask noise
    logging.getLogger('urllib3').setLevel(logging.WARNING)   # Reduce HTTP noise