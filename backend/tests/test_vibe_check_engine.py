"""
Unit tests for the VibeCheckEngine module.
"""

import pytest
import json
import os
from unittest.mock import <PERSON><PERSON>, patch
from vibe_check_engine import VibeCheckEngine, analyze_messages


class TestVibeCheckEngine:
    """Test cases for the VibeCheckEngine class."""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Mock OpenAI client for testing."""
        with patch('vibe_check_engine.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_openai.return_value = mock_client
            yield mock_client
    
    @pytest.fixture
    def engine(self, mock_openai_client):
        """Create a VibeCheckEngine instance with mocked OpenAI client."""
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            return VibeCheckEngine()
    
    def test_harmonious_messages(self, engine, mock_openai_client):
        """Test analysis of harmonious messages."""
        # Mock OpenAI response for harmonious messages
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "summary": "The communication is very positive and collaborative.",
            "tone": "harmony",
            "warnings": [],
            "recommendations": ["Continue the positive communication style."]
        })
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = {"total_tokens": 150}
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        # Test harmonious messages
        messages = [
            "Great work team!",
            "Thanks for your help!",
            "I appreciate the collaboration"
        ]
        
        result = engine.analyze_messages(messages)
        
        # Assertions
        assert result["tone"] == "harmony"
        assert "positive" in result["summary"].lower()
        assert len(result["warnings"]) == 0
        assert len(result["recommendations"]) > 0
        assert "positive" in result["recommendations"][0].lower()
        
        # Verify API call was made with correct parameters
        mock_openai_client.chat.completions.create.assert_called_once()
        call_args = mock_openai_client.chat.completions.create.call_args
        assert call_args[1]["model"] == "gpt-3.5-turbo"
        assert call_args[1]["temperature"] == 0.3
        assert len(call_args[1]["messages"]) == 2  # system + user
    
    def test_conflict_heavy_messages(self, engine, mock_openai_client):
        """Test analysis of conflict-heavy messages."""
        # Mock OpenAI response for conflict messages
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "summary": "The communication shows significant tension and conflict.",
            "tone": "tension",
            "warnings": [
                "Aggressive language detected",
                "Dismissive communication patterns"
            ],
            "recommendations": [
                "Practice active listening",
                "Use 'I' statements instead of 'you' statements",
                "Take a break to cool down before responding"
            ]
        })
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = {"total_tokens": 200}
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        # Test conflict messages
        messages = [
            "This is completely wrong!",
            "You never listen!",
            "I hate this approach!"
        ]
        
        result = engine.analyze_messages(messages)
        
        # Assertions
        assert result["tone"] == "tension"
        assert "tension" in result["summary"].lower() or "conflict" in result["summary"].lower()
        assert len(result["warnings"]) > 0
        assert "aggressive" in result["warnings"][0].lower()
        assert len(result["recommendations"]) > 0
        assert any("listen" in rec.lower() for rec in result["recommendations"])
    
    def test_empty_messages(self, engine, mock_openai_client):
        """Test analysis of empty message list."""
        result = engine.analyze_messages([])
        
        # Should return fallback response without calling API
        assert result["tone"] == "neutral"
        assert "No messages to analyze" in result["summary"]
        assert len(result["warnings"]) == 0
        assert len(result["recommendations"]) == 1
        
        # Verify no API call was made
        mock_openai_client.chat.completions.create.assert_not_called()
    
    def test_openai_authentication_error(self, engine, mock_openai_client):
        """Test handling of OpenAI authentication errors."""
        # Mock authentication error
        mock_openai_client.chat.completions.create.side_effect = Exception("authentication failed")
        
        messages = ["Test message"]
        result = engine.analyze_messages(messages)
        
        # Should return fallback response
        assert result["tone"] == "neutral"
        assert "Authentication failed" in result["summary"]
        assert "AI analysis unavailable" in result["warnings"]
    
    def test_openai_rate_limit_error(self, engine, mock_openai_client):
        """Test handling of OpenAI rate limit errors."""
        # Mock rate limit error
        mock_openai_client.chat.completions.create.side_effect = Exception("rate limit exceeded")
        
        messages = ["Test message"]
        result = engine.analyze_messages(messages)
        
        # Should return fallback response
        assert result["tone"] == "neutral"
        assert "rate limit exceeded" in result["summary"].lower()
        assert "AI analysis unavailable" in result["warnings"]
    
    def test_openai_network_error(self, engine, mock_openai_client):
        """Test handling of network errors."""
        # Mock network error - use a more specific error that doesn't match auth/rate limit patterns
        mock_openai_client.chat.completions.create.side_effect = Exception("Connection timeout")

        messages = ["Test message"]
        result = engine.analyze_messages(messages)

        # Should return fallback response - this will hit the catch-all exception handler
        assert result["tone"] == "neutral"
        assert "Unexpected error occurred during analysis" in result["summary"]
        assert "AI analysis unavailable" in result["warnings"]
    
    def test_invalid_json_response(self, engine, mock_openai_client):
        """Test handling of invalid JSON response from OpenAI."""
        # Mock invalid JSON response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Invalid JSON response"
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = {"total_tokens": 50}
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        messages = ["Test message"]
        result = engine.analyze_messages(messages)
        
        # Should return fallback response
        assert result["tone"] == "neutral"
        assert "Failed to parse AI response" in result["summary"]
        assert "AI analysis unavailable" in result["warnings"]
    
    def test_missing_required_keys(self, engine, mock_openai_client):
        """Test handling of response missing required keys."""
        # Mock response missing some keys
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "summary": "Test summary",
            # Missing tone, warnings, recommendations
        })
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = {"total_tokens": 50}
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        messages = ["Test message"]
        result = engine.analyze_messages(messages)
        
        # Should fill in missing keys with defaults
        assert "summary" in result
        assert result["tone"] == "neutral"  # default
        assert isinstance(result["warnings"], list)
        assert isinstance(result["recommendations"], list)
    
    def test_invalid_tone_value(self, engine, mock_openai_client):
        """Test handling of invalid tone value."""
        # Mock response with invalid tone
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "summary": "Test summary",
            "tone": "invalid_tone",
            "warnings": [],
            "recommendations": []
        })
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = {"total_tokens": 50}
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        messages = ["Test message"]
        result = engine.analyze_messages(messages)
        
        # Should default to neutral
        assert result["tone"] == "neutral"
    
    @patch.dict(os.environ, {'DEBUG_PROMPT': 'true'})
    def test_debug_logging_enabled(self, engine, mock_openai_client, caplog):
        """Test debug logging when DEBUG_PROMPT is enabled."""
        # Mock successful response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "summary": "Test summary",
            "tone": "neutral",
            "warnings": [],
            "recommendations": []
        })
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = {"total_tokens": 50}
        
        mock_openai_client.chat.completions.create.return_value = mock_response
        
        messages = ["Test message"]
        
        with caplog.at_level("DEBUG"):
            result = engine.analyze_messages(messages)
        
        # Check that debug logs were created
        debug_logs = [record for record in caplog.records if record.levelname == "DEBUG"]
        assert len(debug_logs) > 0
        assert any("Raw OpenAI API response" in record.message for record in debug_logs)


class TestConvenienceFunction:
    """Test cases for the convenience analyze_messages function."""
    
    def test_analyze_messages_no_api_key(self):
        """Test convenience function without API key."""
        with patch.dict(os.environ, {}, clear=True):
            # Remove OPENAI_API_KEY if it exists
            if 'OPENAI_API_KEY' in os.environ:
                del os.environ['OPENAI_API_KEY']
            
            result = analyze_messages(["Test message"])
            
            # Should return fallback response
            assert result["tone"] == "neutral"
            assert "missing API key" in result["warnings"][0]
            assert "Set OPENAI_API_KEY" in result["recommendations"][0]
    
    @patch('vibe_check_engine.VibeCheckEngine')
    def test_analyze_messages_with_api_key(self, mock_engine_class):
        """Test convenience function with API key."""
        # Mock the engine instance and its method
        mock_engine = Mock()
        mock_engine.analyze_messages.return_value = {
            "summary": "Test summary",
            "tone": "neutral",
            "warnings": [],
            "recommendations": []
        }
        mock_engine_class.return_value = mock_engine
        
        with patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'}):
            result = analyze_messages(["Test message"])
            
            # Should create engine and call analyze_messages
            mock_engine_class.assert_called_once()
            mock_engine.analyze_messages.assert_called_once_with(["Test message"])
            assert result["tone"] == "neutral"
