"""
Integration tests for the Flask API endpoints.
"""

import pytest
import json
import time
from unittest.mock import patch, Mock
from app import app


class TestFlaskAPI:
    """Test cases for Flask API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the Flask app."""
        app.config['TESTING'] = True
        # Disable rate limiting entirely for testing
        with patch('app.limiter.enabled', False):
            with app.test_client() as client:
                yield client
    
    @pytest.fixture
    def mock_ai_analyze(self):
        """Mock the AI analysis function."""
        with patch('app.ai_analyze_messages') as mock:
            yield mock
    
    @pytest.fixture
    def mock_basic_analyzer(self):
        """Mock the basic analyzer."""
        with patch('app.analyzer') as mock:
            yield mock
    
    def test_valid_analyze_request(self, client, mock_ai_analyze, mock_basic_analyzer):
        """Test valid POST request to /api/v1/analyze."""
        # Mock AI analysis response
        mock_ai_analyze.return_value = {
            "summary": "The communication is positive and collaborative.",
            "tone": "harmony",
            "dignity_score": 7.5,
            "psychological_safety_score": 85,
            "burnout_risk_level": "low",
            "warnings": [],
            "recommendations": ["Continue the positive communication."],
            "patterns": {
                "dignity_violations": [],
                "communication_gaps": [],
                "positive_patterns": ["Encouraging language"]
            }
        }
        
        # Mock basic analysis response
        mock_basic_analyzer.analyze_messages.return_value = {
            "tension_score": 0.5,
            "harmony_score": 4.2,
            "message_count": 2
        }
        
        # Test data
        test_messages = ["Hello team!", "Great work everyone!"]
        
        # Make request
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps(test_messages),
            content_type='application/json'
        )
        
        # Assertions
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data["summary"] == "The communication is positive and collaborative."
        assert data["tone"] == "harmony"
        assert data["dignity_score"] == 7.5
        assert data["psychological_safety_score"] == 85
        assert data["burnout_risk_level"] == "low"
        assert data["warnings"] == []
        assert len(data["recommendations"]) > 0
        assert "patterns" in data
        assert data["patterns"]["positive_patterns"] == ["Encouraging language"]
        assert "basic_analysis" in data
        assert data["basic_analysis"]["tension_score"] == 0.5
        assert data["basic_analysis"]["harmony_score"] == 4.2
        assert data["basic_analysis"]["message_count"] == 2
        
        # Verify mocks were called
        mock_ai_analyze.assert_called_once_with(test_messages)
        mock_basic_analyzer.analyze_messages.assert_called_once_with(test_messages)
    
    def test_invalid_content_type(self, client):
        """Test request with invalid content type."""
        response = client.post(
            '/api/v1/analyze',
            data='["test"]',
            content_type='text/plain'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["error"] == "Request validation failed"
    
    def test_empty_request_body(self, client):
        """Test request with empty body."""
        response = client.post(
            '/api/v1/analyze',
            data='',
            content_type='application/json'
        )

        # Validation now catches JSON decode errors and returns 400
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["error"] == "Request validation failed"
    
    def test_invalid_json(self, client):
        """Test request with invalid JSON."""
        response = client.post(
            '/api/v1/analyze',
            data='{"invalid": json}',
            content_type='application/json'
        )

        # Validation now catches JSON decode errors and returns 400
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["error"] == "Request validation failed"
    
    def test_non_array_payload(self, client, mock_ai_analyze, mock_basic_analyzer):
        """Test request with non-array payload - should now work with {messages: [...]} format."""
        # Mock responses
        mock_ai_analyze.return_value = {
            "summary": "Test",
            "tone": "neutral",
            "dignity_score": 0,
            "psychological_safety_score": 50,
            "burnout_risk_level": "medium",
            "warnings": [],
            "recommendations": [],
            "patterns": {
                "dignity_violations": [],
                "communication_gaps": [],
                "positive_patterns": []
            }
        }
        mock_basic_analyzer.analyze_messages.return_value = {
            "tension_score": 0,
            "harmony_score": 0,
            "message_count": 1
        }
        
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps({"messages": ["test"]}),
            content_type='application/json'
        )
        
        # Should now accept this format
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["tone"] == "neutral"
    
    def test_cors_headers(self, client, mock_ai_analyze, mock_basic_analyzer):
        """Test that CORS headers are present."""
        # Mock responses
        mock_ai_analyze.return_value = {
            "summary": "Test",
            "tone": "neutral",
            "dignity_score": 0,
            "psychological_safety_score": 50,
            "burnout_risk_level": "medium",
            "warnings": [],
            "recommendations": [],
            "patterns": {
                "dignity_violations": [],
                "communication_gaps": [],
                "positive_patterns": []
            }
        }
        mock_basic_analyzer.analyze_messages.return_value = {
            "tension_score": 0,
            "harmony_score": 0,
            "message_count": 1
        }
        
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps(["test"]),
            content_type='application/json'
        )
        
        # Check CORS headers are present
        assert 'Access-Control-Allow-Origin' in response.headers
    
    def test_health_endpoint(self, client):
        """Test the health check endpoint."""
        response = client.get('/api/v1/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "healthy"
        assert data["service"] == "tone-analyzer"
    
    def test_404_error_handler(self, client):
        """Test 404 error handling."""
        response = client.get('/nonexistent')
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data["error"] == "Endpoint not found"
    
    def test_405_error_handler(self, client):
        """Test 405 error handling."""
        response = client.get('/api/v1/analyze')  # GET instead of POST
        
        assert response.status_code == 405
        data = json.loads(response.data)
        assert data["error"] == "Method not allowed"
    
    def test_internal_server_error(self, client, mock_ai_analyze):
        """Test handling of internal server errors."""
        # Mock AI analysis to raise an exception
        mock_ai_analyze.side_effect = Exception("Unexpected error")
        
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps(["test"]),
            content_type='application/json'
        )
        
        assert response.status_code == 500
        data = json.loads(response.data)
        assert data["error"] == "Internal server error occurred during analysis"
    
    @patch.dict('os.environ', {'DEBUG_PROMPT': 'true'})
    def test_debug_logging_in_api(self, client, mock_ai_analyze, mock_basic_analyzer, caplog):
        """Test debug logging configuration in API endpoint."""
        # Mock responses
        mock_ai_analyze.return_value = {
            "summary": "Test",
            "tone": "neutral",
            "dignity_score": 0,
            "psychological_safety_score": 50,
            "burnout_risk_level": "medium",
            "warnings": [],
            "recommendations": [],
            "patterns": {
                "dignity_violations": [],
                "communication_gaps": [],
                "positive_patterns": []
            }
        }
        mock_basic_analyzer.analyze_messages.return_value = {
            "tension_score": 0,
            "harmony_score": 0,
            "message_count": 1
        }
        
        test_messages = ["Debug test message"]
        
        with caplog.at_level("INFO"):
            response = client.post(
                '/api/v1/analyze',
                data=json.dumps(test_messages),
                content_type='application/json'
            )
        
        assert response.status_code == 200
        
        # Check that INFO logs were created (secure logging)
        info_logs = [record for record in caplog.records if record.levelname == "INFO"]
        assert len(info_logs) > 0
        assert any("API request to /api/v1/analyze" in record.message for record in info_logs)
    
    def test_large_message_array(self, client, mock_ai_analyze, mock_basic_analyzer):
        """Test handling of large message arrays."""
        # Mock responses
        mock_ai_analyze.return_value = {
            "summary": "Large dataset analysis",
            "tone": "neutral",
            "dignity_score": 0,
            "psychological_safety_score": 50,
            "burnout_risk_level": "medium",
            "warnings": [],
            "recommendations": [],
            "patterns": {
                "dignity_violations": [],
                "communication_gaps": [],
                "positive_patterns": []
            }
        }
        mock_basic_analyzer.analyze_messages.return_value = {
            "tension_score": 1.0,
            "harmony_score": 1.0,
            "message_count": 100
        }
        
        # Create large message array
        large_messages = [f"Message {i}" for i in range(100)]
        
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps(large_messages),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["basic_analysis"]["message_count"] == 100
    
    def test_empty_message_array(self, client, mock_ai_analyze, mock_basic_analyzer):
        """Test handling of empty message array - should now be rejected by validation."""
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps([]),
            content_type='application/json'
        )
        
        # Empty arrays are now rejected by validation
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data["error"] == "Invalid request data"
        assert "Length must be between 1 and 100" in str(data["details"])
    
    def test_special_characters_in_messages(self, client, mock_ai_analyze, mock_basic_analyzer):
        """Test handling of messages with special characters."""
        # Small delay to avoid rate limiting
        time.sleep(0.1)
        
        # Mock responses
        mock_ai_analyze.return_value = {
            "summary": "Messages with special characters analyzed",
            "tone": "neutral",
            "dignity_score": 0,
            "psychological_safety_score": 50,
            "burnout_risk_level": "medium",
            "warnings": [],
            "recommendations": [],
            "patterns": {
                "dignity_violations": [],
                "communication_gaps": [],
                "positive_patterns": []
            }
        }
        mock_basic_analyzer.analyze_messages.return_value = {
            "tension_score": 0,
            "harmony_score": 0,
            "message_count": 3
        }
        
        special_messages = [
            "Hello! 😊",
            "This costs $100.00",
            "Email: <EMAIL>"
        ]
        
        response = client.post(
            '/api/v1/analyze',
            data=json.dumps(special_messages),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["basic_analysis"]["message_count"] == 3
