"""
Tests for Slack integration functionality.
"""

import pytest
import json
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from slack_sdk.errors import SlackApiError
import os
from app import app
from slack_connector import SlackConnector


class TestSlackConnector:
    """Test cases for SlackConnector class."""
    
    @pytest.fixture
    def mock_slack_client(self):
        """Mock Slack WebClient."""
        with patch('slack_connector.WebClient') as mock_client:
            yield mock_client
    
    @pytest.fixture
    def slack_connector(self, mock_slack_client):
        """Create SlackConnector instance with mocked client."""
        with patch.dict(os.environ, {'SLACK_BOT_TOKEN': 'xoxb-test-token'}):
            # Mock auth_test response for connection test
            mock_instance = Mock()
            mock_instance.auth_test.return_value = {
                'team': 'Test Team',
                'user': 'test_bot'
            }
            mock_slack_client.return_value = mock_instance
            
            connector = SlackConnector()
            yield connector
    
    def test_init_with_token(self, mock_slack_client):
        """Test SlackConnector initialization with token."""
        mock_instance = Mock()
        mock_instance.auth_test.return_value = {'team': 'Test', 'user': 'bot'}
        mock_slack_client.return_value = mock_instance
        
        connector = SlackConnector(token="test-token")
        assert connector.token == "test-token"
        mock_slack_client.assert_called_once_with(token="test-token")
        mock_instance.auth_test.assert_called_once()
    
    def test_init_with_env_var(self, mock_slack_client):
        """Test SlackConnector initialization with environment variable."""
        mock_instance = Mock()
        mock_instance.auth_test.return_value = {'team': 'Test', 'user': 'bot'}
        mock_slack_client.return_value = mock_instance
        
        with patch.dict(os.environ, {'SLACK_BOT_TOKEN': 'env-token'}):
            connector = SlackConnector()
            assert connector.token == "env-token"
    
    def test_init_no_token(self):
        """Test SlackConnector initialization without token."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="Slack Bot Token not found"):
                SlackConnector()
    
    def test_connection_failure(self, mock_slack_client):
        """Test connection failure handling."""
        mock_instance = Mock()
        mock_instance.auth_test.side_effect = SlackApiError(
            message="Invalid token",
            response={'error': 'invalid_auth'}
        )
        mock_slack_client.return_value = mock_instance
        
        with patch.dict(os.environ, {'SLACK_BOT_TOKEN': 'invalid-token'}):
            with pytest.raises(ConnectionError, match="Slack API connection failed"):
                SlackConnector()
    
    def test_get_channels(self, slack_connector):
        """Test getting channels list."""
        mock_response = {
            'channels': [
                {
                    'id': 'C123456',
                    'name': 'general',
                    'is_private': False,
                    'num_members': 10,
                    'purpose': {'value': 'General discussion'},
                    'topic': {'value': 'Welcome'}
                },
                {
                    'id': 'C789012',
                    'name': 'random',
                    'is_private': True,
                    'num_members': 5,
                    'purpose': {'value': ''},
                    'topic': {'value': ''}
                }
            ]
        }
        
        slack_connector.client.conversations_list.return_value = mock_response
        
        channels = slack_connector.get_channels()
        
        assert len(channels) == 2
        assert channels[0]['id'] == 'C123456'
        assert channels[0]['name'] == 'general'
        assert channels[0]['is_private'] == False
        assert channels[0]['member_count'] == 10
        assert channels[1]['is_private'] == True
        
        slack_connector.client.conversations_list.assert_called_once_with(
            types="public_channel,private_channel",
            exclude_archived=True,
            limit=1000
        )
    
    def test_get_channels_api_error(self, slack_connector):
        """Test get_channels with API error."""
        slack_connector.client.conversations_list.side_effect = SlackApiError(
            message="API error",
            response={'error': 'channel_not_found'}
        )
        
        with pytest.raises(RuntimeError, match="Failed to retrieve channels"):
            slack_connector.get_channels()
    
    def test_get_channel_messages(self, slack_connector):
        """Test getting channel messages."""
        # Mock channel info
        slack_connector.client.conversations_info.return_value = {
            'channel': {'name': 'general'}
        }
        
        # Mock messages response
        mock_messages = {
            'messages': [
                {'text': 'Hello team!', 'user': 'U123', 'ts': '1234567890.123'},
                {'text': 'Great work', 'user': 'U456', 'ts': '1234567891.123'},
                {'text': '', 'user': 'U789', 'subtype': 'bot_message'},  # Should be filtered
                {'subtype': 'channel_join', 'user': 'U999'}  # Should be filtered
            ]
        }
        slack_connector.client.conversations_history.return_value = mock_messages
        
        messages, metadata = slack_connector.get_channel_messages('C123456', days_back=7, limit=100)
        
        assert len(messages) == 2
        assert messages[0] == 'Hello team!'
        assert messages[1] == 'Great work'
        
        assert metadata['channel_id'] == 'C123456'
        assert metadata['channel_name'] == 'general'
        assert metadata['date_range'] == '7 days'
        assert metadata['total_messages'] == 4
        assert metadata['filtered_messages'] == 2
        assert metadata['unique_users'] == 2
        assert 'U123' in metadata['users']
        assert 'U456' in metadata['users']
    
    def test_clean_message_text(self, slack_connector):
        """Test message text cleaning."""
        # Test user mentions
        assert slack_connector._clean_message_text("Hello <@U123456>") == "Hello"
        
        # Test channel mentions
        assert slack_connector._clean_message_text("Check <#C123456|general>") == "Check"
        
        # Test URLs
        assert slack_connector._clean_message_text("Visit <https://example.com|Example>") == "Visit"
        
        # Test complex message
        complex_msg = "Hey <@U123> check <#C456|general> and visit <https://example.com>"
        assert slack_connector._clean_message_text(complex_msg) == "Hey check and visit"
        
        # Test empty result
        assert slack_connector._clean_message_text("<@U123>") == ""
        assert slack_connector._clean_message_text("   ") == ""
    
    def test_get_user_info(self, slack_connector):
        """Test getting user info."""
        mock_response = {
            'user': {
                'id': 'U123456',
                'name': 'john.doe',
                'real_name': 'John Doe',
                'profile': {
                    'display_name': 'Johnny',
                    'title': 'Developer'
                },
                'is_bot': False
            }
        }
        slack_connector.client.users_info.return_value = mock_response
        
        user_info = slack_connector.get_user_info('U123456')
        
        assert user_info['id'] == 'U123456'
        assert user_info['name'] == 'john.doe'
        assert user_info['real_name'] == 'John Doe'
        assert user_info['display_name'] == 'Johnny'
        assert user_info['title'] == 'Developer'
        assert user_info['is_bot'] == False
    
    def test_get_user_info_error(self, slack_connector):
        """Test get_user_info with API error."""
        slack_connector.client.users_info.side_effect = SlackApiError(
            message="User not found",
            response={'error': 'user_not_found'}
        )
        
        user_info = slack_connector.get_user_info('U999999')
        
        assert user_info['id'] == 'U999999'
        assert user_info['name'] == 'Unknown User'
        assert user_info['is_bot'] == False


class TestSlackAPIEndpoints:
    """Test cases for Slack-related Flask API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create a test client for the Flask app."""
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client
    
    @pytest.fixture
    def mock_slack_connector(self):
        """Mock SlackConnector class."""
        with patch('app.SlackConnector') as mock:
            yield mock
    
    def test_slack_channels_endpoint(self, client, mock_slack_connector):
        """Test GET /api/v1/slack/channels endpoint."""
        # Mock connector instance
        mock_instance = Mock()
        mock_instance.get_channels.return_value = [
            {'id': 'C123', 'name': 'general', 'member_count': 10},
            {'id': 'C456', 'name': 'random', 'member_count': 5}
        ]
        mock_slack_connector.return_value = mock_instance
        
        response = client.get('/api/v1/slack/channels')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['count'] == 2
        assert len(data['channels']) == 2
        assert data['channels'][0]['name'] == 'general'
    
    def test_slack_channels_no_token(self, client, mock_slack_connector):
        """Test channels endpoint without Slack token."""
        mock_slack_connector.side_effect = ValueError("Slack Bot Token not found")
        
        response = client.get('/api/v1/slack/channels')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "Slack not configured" in data['error']
    
    def test_slack_analyze_channel(self, client, mock_slack_connector):
        """Test POST /api/v1/slack/analyze-channel endpoint."""
        # Mock connector instance
        mock_instance = Mock()
        mock_instance.get_channel_messages.return_value = (
            ['Hello team!', 'Great work!'],
            {
                'channel_id': 'C123',
                'channel_name': 'general',
                'date_range': '7 days',
                'total_messages': 2,
                'filtered_messages': 2,
                'unique_users': 2,
                'users': ['U123', 'U456']
            }
        )
        mock_slack_connector.return_value = mock_instance
        
        # Mock AI analysis
        with patch('app.ai_analyze_messages') as mock_ai, \
             patch('app.analyzer') as mock_basic:
            
            mock_ai.return_value = {
                'summary': 'Positive team communication',
                'tone': 'harmony',
                'dignity_score': 7.5,
                'psychological_safety_score': 85,
                'burnout_risk_level': 'low',
                'warnings': [],
                'recommendations': ['Keep up the positive communication'],
                'patterns': {
                    'dignity_violations': [],
                    'communication_gaps': [],
                    'positive_patterns': ['Encouraging language']
                }
            }
            
            mock_basic.analyze_messages.return_value = {
                'tension_score': 0.2,
                'harmony_score': 4.8,
                'message_count': 2
            }
            
            request_data = {
                'channel_id': 'C123',
                'days_back': 7,
                'limit': 100
            }
            
            response = client.post(
                '/api/v1/slack/analyze-channel',
                data=json.dumps(request_data),
                content_type='application/json'
            )
            
            assert response.status_code == 200
            data = json.loads(response.data)
            
            assert data['tone'] == 'harmony'
            assert data['dignity_score'] == 7.5
            assert data['psychological_safety_score'] == 85
            assert data['burnout_risk_level'] == 'low'
            assert 'slack_metadata' in data
            assert data['slack_metadata']['channel_name'] == 'general'
            assert data['slack_metadata']['filtered_messages'] == 2
    
    def test_slack_analyze_channel_invalid_request(self, client):
        """Test analyze channel endpoint with invalid request."""
        # Missing channel_id
        response = client.post(
            '/api/v1/slack/analyze-channel',
            data=json.dumps({'days_back': 7}),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['error'] == 'Invalid request data'
        assert 'channel_id' in str(data['details'])
    
    def test_slack_analyze_channel_invalid_days_back(self, client):
        """Test analyze channel endpoint with invalid days_back."""
        request_data = {
            'channel_id': 'C123',
            'days_back': 50  # Too many days
        }
        
        response = client.post(
            '/api/v1/slack/analyze-channel',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['error'] == 'Invalid request data'
        assert 'days_back' in str(data['details'])
    
    def test_slack_analyze_channel_no_messages(self, client, mock_slack_connector):
        """Test analyze channel endpoint with no messages found."""
        mock_instance = Mock()
        mock_instance.get_channel_messages.return_value = (
            [],  # No messages
            {'channel_id': 'C123', 'channel_name': 'empty', 'total_messages': 0}
        )
        mock_slack_connector.return_value = mock_instance
        
        request_data = {'channel_id': 'C123'}
        
        response = client.post(
            '/api/v1/slack/analyze-channel',
            data=json.dumps(request_data),
            content_type='application/json'
        )
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'No messages found' in data['error']
    
    def test_slack_test_connection(self, client, mock_slack_connector):
        """Test GET /api/v1/slack/test-connection endpoint."""
        mock_instance = Mock()
        mock_instance.get_channels.return_value = [{'id': 'C123'}] * 5
        mock_slack_connector.return_value = mock_instance
        
        response = client.get('/api/v1/slack/test-connection')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'connected'
        assert data['channel_count'] == 5
        assert 'successful' in data['message']
    
    def test_slack_test_connection_failure(self, client, mock_slack_connector):
        """Test connection test with failure."""
        mock_slack_connector.side_effect = ValueError("Invalid token")
        
        response = client.get('/api/v1/slack/test-connection')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['status'] == 'configuration_error'


class TestSlackTestFunction:
    """Test the standalone test function."""
    
    def test_slack_connection_success(self):
        """Test successful connection test."""
        with patch('slack_connector.SlackConnector') as mock_connector:
            mock_instance = Mock()
            mock_instance.get_channels.return_value = [{'id': 'C123'}] * 3
            mock_connector.return_value = mock_instance
            
            from slack_connector import test_slack_connection
            result = test_slack_connection()
            
            assert result == True
            mock_connector.assert_called_once()
    
    def test_slack_connection_failure(self):
        """Test failed connection test."""
        with patch('slack_connector.SlackConnector') as mock_connector:
            mock_connector.side_effect = Exception("Connection failed")
            
            from slack_connector import test_slack_connection
            result = test_slack_connection()
            
            assert result == False


# Integration test that requires actual environment setup
class TestSlackIntegrationLive:
    """Live integration tests (only run if Slack token is available)."""
    
    @pytest.mark.skipif(
        not os.getenv('SLACK_BOT_TOKEN'),
        reason="SLACK_BOT_TOKEN not set - skipping live tests"
    )
    def test_real_slack_connection(self):
        """Test with real Slack connection (only if token available)."""
        connector = SlackConnector()
        channels = connector.get_channels()
        
        # Basic validation that we got channels
        assert isinstance(channels, list)
        if channels:
            assert 'id' in channels[0]
            assert 'name' in channels[0]
    
    @pytest.mark.skipif(
        not os.getenv('SLACK_BOT_TOKEN'),
        reason="SLACK_BOT_TOKEN not set - skipping live tests"
    )
    def test_real_message_retrieval(self):
        """Test real message retrieval (only if token available)."""
        connector = SlackConnector()
        channels = connector.get_channels()
        
        if channels:
            # Try to get messages from first channel
            channel_id = channels[0]['id']
            messages, metadata = connector.get_channel_messages(
                channel_id, 
                days_back=1, 
                limit=10
            )
            
            assert isinstance(messages, list)
            assert isinstance(metadata, dict)
            assert 'channel_name' in metadata
            assert 'total_messages' in metadata