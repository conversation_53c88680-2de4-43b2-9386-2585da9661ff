"""
Example integration of vibe_check_engine with the existing Flask app.

This shows how to add an AI-powered analysis endpoint alongside the existing basic analyzer.
"""

from flask import Flask, request, jsonify
import logging
from vibe_check_engine import analyze_messages as ai_analyze_messages
from app import analyzer  # Import the existing basic analyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/analyze/ai', methods=['POST'])
def analyze_communication_ai():
    """Analyze communication tone using AI-powered analysis."""
    try:
        # Validate request content type
        if not request.is_json:
            logger.warning("Request received without JSON content type")
            return jsonify({
                'error': 'Content-Type must be application/json'
            }), 400
        
        # Get JSON data
        data = request.get_json()
        
        # Validate that data is provided
        if data is None:
            logger.warning("No JSON data provided in request")
            return jsonify({
                'error': 'No JSON data provided'
            }), 400
        
        # Validate that data is a list
        if not isinstance(data, list):
            logger.warning(f"Invalid data type received: {type(data)}")
            return jsonify({
                'error': 'Expected an array of messages'
            }), 400
        
        # Log the analysis request
        logger.info(f"Analyzing {len(data)} messages with AI")
        
        # Perform AI-powered tone analysis
        result = ai_analyze_messages(data)
        
        logger.info(f"AI Analysis complete: tone={result['tone']}")
        
        return jsonify(result), 200
        
    except Exception as e:
        logger.error(f"Error during AI analysis: {str(e)}")
        return jsonify({
            'error': 'Internal server error occurred during AI analysis'
        }), 500

@app.route('/analyze/compare', methods=['POST'])
def compare_analysis():
    """Compare basic and AI analysis side by side."""
    try:
        # Validate request
        if not request.is_json:
            return jsonify({'error': 'Content-Type must be application/json'}), 400
        
        data = request.get_json()
        if not isinstance(data, list):
            return jsonify({'error': 'Expected an array of messages'}), 400
        
        logger.info(f"Comparing analysis methods for {len(data)} messages")
        
        # Get both analyses
        basic_result = analyzer.analyze_messages(data)
        ai_result = ai_analyze_messages(data)
        
        # Combine results
        comparison = {
            'basic_analysis': basic_result,
            'ai_analysis': ai_result,
            'message_count': len(data)
        }
        
        logger.info("Comparison analysis complete")
        return jsonify(comparison), 200
        
    except Exception as e:
        logger.error(f"Error during comparison analysis: {str(e)}")
        return jsonify({
            'error': 'Internal server error occurred during comparison'
        }), 500

@app.route('/api/v1/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'vibe-check-with-ai'}), 200

if __name__ == '__main__':
    logger.info("Starting Flask application with AI integration...")
    app.run(debug=True, host='0.0.0.0', port=5009)
