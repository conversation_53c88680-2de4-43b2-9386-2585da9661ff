"""
Input validation utilities for secure request handling.
"""

from marshmallow import Schema, fields, validate, ValidationError
from flask import jsonify
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class MessageAnalysisSchema(Schema):
    """Schema for validating message analysis requests."""
    # Expect array of messages, each string max 2000 chars, max 100 messages
    messages = fields.List(
        fields.String(validate=validate.Length(min=1, max=2000)),
        validate=validate.Length(min=1, max=100),
        required=True
    )

class SlackAnalysisSchema(Schema):
    """Schema for validating Slack channel analysis requests."""
    channel_id = fields.String(
        required=True,
        validate=[
            validate.Length(min=1, max=20),
            validate.Regexp(r'^[A-Z0-9]+$', error='Invalid channel ID format')
        ]
    )
    days_back = fields.Integer(
        validate=validate.Range(min=1, max=30),
        load_default=7
    )
    limit = fields.Integer(
        validate=validate.Range(min=1, max=1000),
        load_default=100
    )

def validate_json(schema_class):
    """
    Decorator to validate JSON request data against a schema.
    
    Args:
        schema_class: Marshmallow schema class to validate against
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Handle direct array input for /api/v1/analyze
                if schema_class == MessageAnalysisSchema:
                    from flask import request
                    data = request.get_json()
                    if isinstance(data, list):
                        # Convert list to dict format expected by schema
                        data = {'messages': data}
                    validated_data = schema_class().load(data)
                    # Extract just the messages for backward compatibility
                    request.validated_data = validated_data['messages']
                else:
                    from flask import request
                    data = request.get_json()
                    validated_data = schema_class().load(data)
                    request.validated_data = validated_data
                    
            except ValidationError as err:
                logger.warning(f"Validation error: {err.messages}")
                return jsonify({
                    'error': 'Invalid request data',
                    'details': err.messages
                }), 400
            except Exception as err:
                logger.error(f"Validation exception: {err}")
                return jsonify({
                    'error': 'Request validation failed'
                }), 400
                
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def sanitize_string(text: str, max_length: int = 1000) -> str:
    """
    Sanitize string input by removing dangerous characters and limiting length.
    
    Args:
        text: Input string to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
    """
    if not isinstance(text, str):
        return ""
    
    # Remove null bytes and control characters (except newlines and tabs)
    sanitized = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
    
    # Limit length
    return sanitized[:max_length]